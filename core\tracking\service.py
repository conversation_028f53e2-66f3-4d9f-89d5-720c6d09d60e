"""Document processing tracking service for analytics and monitoring."""

import time
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Callable
from contextlib import contextmanager

from .models import (
    TrackingDatabase, ProcessingEvent, ProcessingStatistics,
    ProcessingStatus, OperationType
)


class DocumentTrackingService:
    """Service for tracking document processing events and generating analytics."""
    
    def __init__(self, db_path: str = None, enabled: bool = True):
        """
        Initialize the tracking service.
        
        Args:
            db_path: Path to the SQLite database file
            enabled: Whether tracking is enabled (can be disabled for testing)
        """
        self.enabled = enabled
        self.db = TrackingDatabase(db_path) if enabled else None
    
    def is_enabled(self) -> bool:
        """Check if tracking is enabled."""
        return self.enabled and self.db is not None
    
    @contextmanager
    def track_processing(
        self,
        tenant_name: str,
        mailbox_email: str,
        filename: str,
        file_size: int,
        operation_type: OperationType = OperationType.FULL_PROCESSING
    ):
        """
        Context manager for tracking document processing operations.
        
        Usage:
            with tracker.track_processing(tenant, mailbox, filename, size) as tracker:
                # Do processing work
                tracker.set_document_type("invoice")
                tracker.set_upload_folder("Invoices/2024/Company")
                # If an exception occurs, it will be automatically logged as failed
        """
        if not self.is_enabled():
            yield _DummyTracker()
            return
        
        start_time = time.time()
        tracker = _ProcessingTracker(
            tenant_name=tenant_name,
            mailbox_email=mailbox_email,
            filename=filename,
            file_size=file_size,
            operation_type=operation_type,
            start_time=start_time
        )
        
        try:
            yield tracker
            # If we get here without exception, mark as success
            if tracker.status is None:
                tracker.status = ProcessingStatus.SUCCESS
        except Exception as e:
            # Log the exception and mark as failed
            tracker.status = ProcessingStatus.FAILED
            tracker.error_message = str(e)
            raise
        finally:
            # Log the event regardless of success/failure
            if tracker.status is not None:
                processing_time = int((time.time() - start_time) * 1000)  # Convert to milliseconds
                
                event = ProcessingEvent(
                    tenant_name=tracker.tenant_name,
                    mailbox_email=tracker.mailbox_email,
                    document_type=tracker.document_type or "Unknown",
                    filename=tracker.filename,
                    file_size=tracker.file_size,
                    processing_date=datetime.now(),
                    status=tracker.status,
                    operation_type=tracker.operation_type,
                    upload_folder=tracker.upload_folder,
                    notification_recipients=tracker.notification_recipients,
                    error_message=tracker.error_message,
                    processing_time_ms=processing_time,
                    extracted_data=tracker.extracted_data
                )
                
                self.log_event(event)
    
    def log_event(self, event: ProcessingEvent) -> bool:
        """Log a processing event directly."""
        if not self.is_enabled():
            return True
        
        return self.db.log_processing_event(event)
    
    def log_simple_event(
        self,
        tenant_name: str,
        mailbox_email: str,
        document_type: str,
        filename: str,
        file_size: int,
        status: ProcessingStatus,
        operation_type: OperationType = OperationType.FULL_PROCESSING,
        upload_folder: str = None,
        notification_recipients: List[str] = None,
        error_message: str = None,
        extracted_data: Dict[str, Any] = None
    ) -> bool:
        """Log a simple processing event without using the context manager."""
        if not self.is_enabled():
            return True
        
        event = ProcessingEvent(
            tenant_name=tenant_name,
            mailbox_email=mailbox_email,
            document_type=document_type,
            filename=filename,
            file_size=file_size,
            processing_date=datetime.now(),
            status=status,
            operation_type=operation_type,
            upload_folder=upload_folder,
            notification_recipients=notification_recipients,
            error_message=error_message,
            processing_time_ms=None,
            extracted_data=extracted_data
        )
        
        return self.log_event(event)
    
    def get_daily_stats(self, tenant_name: str, target_date: date) -> ProcessingStatistics:
        """Get statistics for a specific day."""
        if not self.is_enabled():
            return self._empty_stats(tenant_name, target_date, target_date)
        
        stats_list = self.db.get_daily_statistics(tenant_name, target_date, target_date)
        return stats_list[0] if stats_list else self._empty_stats(tenant_name, target_date, target_date)
    
    def get_monthly_stats(self, tenant_name: str, year: int, month: int) -> ProcessingStatistics:
        """Get aggregated statistics for a specific month."""
        if not self.is_enabled():
            start_date = date(year, month, 1)
            from calendar import monthrange
            _, last_day = monthrange(year, month)
            end_date = date(year, month, last_day)
            return self._empty_stats(tenant_name, start_date, end_date)
        
        return self.db.get_monthly_statistics(tenant_name, year, month)
    
    def get_yearly_stats(self, tenant_name: str, year: int) -> ProcessingStatistics:
        """Get aggregated statistics for a specific year."""
        if not self.is_enabled():
            start_date = date(year, 1, 1)
            end_date = date(year, 12, 31)
            return self._empty_stats(tenant_name, start_date, end_date)
        
        return self.db.get_yearly_statistics(tenant_name, year)
    
    def get_recent_events(self, tenant_name: str, limit: int = 50) -> List[ProcessingEvent]:
        """Get recent processing events for troubleshooting."""
        if not self.is_enabled():
            return []
        
        return self.db.get_recent_events(tenant_name, limit)
    
    def get_failed_events(self, tenant_name: str, days_back: int = 7) -> List[ProcessingEvent]:
        """Get recent failed events for troubleshooting."""
        if not self.is_enabled():
            return []
        
        return self.db.get_failed_events(tenant_name, days_back)
    
    def get_date_range_stats(self, tenant_name: str, start_date: date, end_date: date) -> List[ProcessingStatistics]:
        """Get daily statistics for a date range."""
        if not self.is_enabled():
            return []
        
        return self.db.get_daily_statistics(tenant_name, start_date, end_date)
    
    def _empty_stats(self, tenant_name: str, start_date: date, end_date: date) -> ProcessingStatistics:
        """Create empty statistics for when tracking is disabled."""
        return ProcessingStatistics(
            tenant_name=tenant_name,
            period_start=start_date,
            period_end=end_date,
            total_documents=0,
            successful_documents=0,
            failed_documents=0,
            documents_by_type={},
            total_file_size=0
        )


class _ProcessingTracker:
    """Internal tracker for a single processing operation."""
    
    def __init__(self, tenant_name: str, mailbox_email: str, filename: str, 
                 file_size: int, operation_type: OperationType, start_time: float):
        self.tenant_name = tenant_name
        self.mailbox_email = mailbox_email
        self.filename = filename
        self.file_size = file_size
        self.operation_type = operation_type
        self.start_time = start_time
        
        # Fields to be set during processing
        self.document_type: Optional[str] = None
        self.upload_folder: Optional[str] = None
        self.notification_recipients: Optional[List[str]] = None
        self.error_message: Optional[str] = None
        self.status: Optional[ProcessingStatus] = None
        self.extracted_data: Optional[Dict[str, Any]] = None
    
    def set_document_type(self, doc_type: str):
        """Set the detected document type."""
        self.document_type = doc_type
    
    def set_upload_folder(self, folder: str):
        """Set the upload folder path."""
        self.upload_folder = folder
    
    def set_notification_recipients(self, recipients: List[str]):
        """Set the list of notification recipients."""
        self.notification_recipients = recipients
    
    def set_error(self, error_message: str):
        """Set an error message and mark as failed."""
        self.error_message = error_message
        self.status = ProcessingStatus.FAILED
    
    def set_success(self):
        """Mark the operation as successful."""
        self.status = ProcessingStatus.SUCCESS
    
    def set_partial_success(self):
        """Mark the operation as partially successful."""
        self.status = ProcessingStatus.PARTIAL
    
    def set_extracted_data(self, data: Dict[str, Any]):
        """Set extracted document data."""
        self.extracted_data = data


class _DummyTracker:
    """Dummy tracker for when tracking is disabled."""
    
    def set_document_type(self, doc_type: str):
        pass
    
    def set_upload_folder(self, folder: str):
        pass
    
    def set_notification_recipients(self, recipients: List[str]):
        pass
    
    def set_error(self, error_message: str):
        pass
    
    def set_success(self):
        pass
    
    def set_partial_success(self):
        pass
    
    def set_extracted_data(self, data: Dict[str, Any]):
        pass


# Global tracking service instance
_tracking_service: Optional[DocumentTrackingService] = None


def get_tracking_service() -> DocumentTrackingService:
    """Get the global tracking service instance."""
    global _tracking_service
    if _tracking_service is None:
        _tracking_service = DocumentTrackingService()
    return _tracking_service


def initialize_tracking(db_path: str = None, enabled: bool = True):
    """Initialize the global tracking service with custom settings."""
    global _tracking_service
    _tracking_service = DocumentTrackingService(db_path, enabled)
