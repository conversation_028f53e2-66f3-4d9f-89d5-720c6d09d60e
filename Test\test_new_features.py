#!/usr/bin/env python3
"""
Test script for the new features:
1. Enhanced subfolder format with company name and document year
2. Structured summaries from ChatGPT
3. Preferred language support
4. External email filtering
"""

import sys
import os
import json

# Add parent directory to path so we can import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.router import resolve
from core.interpreter.chatgpt_api import analyze_mail_and_pdf

def test_router_with_extracted_fields():
    """Test the enhanced router with extracted fields."""
    print("🧪 Testing enhanced router functionality...")
    
    # Sample tenant config
    tenant_config = {
        "defaults": {
            "storage": {"subfolder_format": "{doc_type}/{yyyy}"},
            "preferred_language": "English"
        },
        "document_types": {
            "certificate_of_analysis": {
                "storage": {"subfolder_format": "Certificates/{document_year}/{company_name}"}
            }
        }
    }
    
    # Sample extracted fields
    extracted_fields = {
        "company_name": "ACME Chemical Corp",
        "signing_date": "2018-03-15",
        "batch_number": "BC2018-001",
        "ph_level": "7.2",
        "acidity": "0.05%"
    }
    
    # Test with extracted fields
    folder_path = resolve("certificate_of_analysis", tenant_config, extracted_fields)
    print(f"📁 Generated folder path: {folder_path}")
    
    # Expected: Certificates/2018/ACME Chemical Corp
    expected = "Certificates/2018/ACME Chemical Corp"
    if folder_path == expected:
        print("✅ Router test passed!")
    else:
        print(f"❌ Router test failed. Expected: {expected}, Got: {folder_path}")
    
    # Test without extracted fields (fallback)
    folder_path_fallback = resolve("certificate_of_analysis", tenant_config)
    print(f"📁 Fallback folder path: {folder_path_fallback}")

def test_language_parameter():
    """Test language parameter functionality (mock test)."""
    print("\n🧪 Testing language parameter...")
    
    # This would normally call OpenAI, so we'll just test the parameter passing
    try:
        # Mock test - just verify the function accepts the language parameter
        from core.interpreter.chatgpt_api import _build_messages
        
        messages = _build_messages("Test email", "Test PDF content", "Swedish")
        
        # Check if language instruction was added to system prompt
        system_content = messages[0]["content"]
        if "Swedish" in system_content:
            print("✅ Language parameter test passed!")
        else:
            print("❌ Language parameter not found in system prompt")
            
    except Exception as e:
        print(f"❌ Language test failed: {e}")

def test_external_email_filtering():
    """Test the external email filtering functionality."""
    print("\n🧪 Testing external email filtering...")

    try:
        from core.mail_reader import _is_external_email

        # Test config with external-only filtering enabled
        tenant_config = {
            "defaults": {
                "email_filtering": {
                    "process_external_only": True,
                    "company_domains": ["acme-corp.com", "company.com"]
                }
            }
        }

        # Test external emails (should be processed)
        external_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        for email in external_emails:
            if _is_external_email(email, tenant_config):
                print(f"✅ {email} correctly identified as external")
            else:
                print(f"❌ {email} incorrectly identified as internal")

        # Test internal emails (should be skipped)
        internal_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"  # Test case insensitive
        ]

        for email in internal_emails:
            if not _is_external_email(email, tenant_config):
                print(f"✅ {email} correctly identified as internal")
            else:
                print(f"❌ {email} incorrectly identified as external")

        # Test with filtering disabled (should process all)
        config_disabled = {
            "defaults": {
                "email_filtering": {
                    "process_external_only": False,
                    "company_domains": ["acme-corp.com"]
                }
            }
        }

        if _is_external_email("<EMAIL>", config_disabled):
            print("✅ Filtering disabled - internal email processed")
        else:
            print("❌ Filtering disabled but email still filtered")

    except Exception as e:
        print(f"❌ External email filtering test failed: {e}")


def test_config_structure():
    """Test the updated config structure."""
    print("\n🧪 Testing config structure...")

    try:
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)

        # Check for preferred_language
        if "preferred_language" in config.get("defaults", {}):
            print("✅ preferred_language found in defaults")
        else:
            print("❌ preferred_language missing from defaults")

        # Check for email_filtering
        email_filtering = config.get("defaults", {}).get("email_filtering", {})
        if "process_external_only" in email_filtering and "company_domains" in email_filtering:
            print("✅ email_filtering configuration found")
        else:
            print("❌ email_filtering configuration missing")

        # Check for enhanced certificate_of_analysis config
        coa_config = config.get("document_types", {}).get("certificate_of_analysis", {})
        storage_format = coa_config.get("storage", {}).get("subfolder_format", "")

        if "{document_year}" in storage_format and "{company_name}" in storage_format:
            print("✅ Enhanced subfolder format found for certificate_of_analysis")
        else:
            print("❌ Enhanced subfolder format missing")

    except Exception as e:
        print(f"❌ Config test failed: {e}")

if __name__ == "__main__":
    print("🚀 Testing new Mail_Auto features...\n")

    test_router_with_extracted_fields()
    test_language_parameter()
    test_external_email_filtering()
    test_config_structure()

    print("\n✨ Test completed!")
