# 🧠 GitHub Workflow – Step-by-Step Guide

A professional, step-by-step guide to managing your Git workflow like a senior developer.

---

###############################################################################
## Login to github

```bash
# 1. Set your Git username globally
git config --global user.name "YOUR_GITHUB_USERNAME"

# 2. Set your Git email globally (use the email associated with your GitHub account)
git config --global user.email "<EMAIL>"

# 3. Cache your GitHub credentials (stores them securely)
# For Windows:
git config --global credential.helper wincred

# For macOS:
# git config --global credential.helper osxkeychain

# For Linux:
# git config --global credential.helper cache
# Optional: Set cache timeout (e.g., 1 hour = 3600 seconds)
# git config --global credential.helper 'cache --timeout=3600'

# 4. Verify your configuration
git config --list

# Note: For more secure authentication, its recommended to use SSH keys
# or GitHub CLI (gh) for authentication. The above method uses HTTPS.

# Optional: If you want to use GitHub CLI:
# 1. Install GitHub CLI (gh)
# 2. Authenticate with:
# gh auth login
```

###############################################################################
## Initializing github repository

```bash
# 1. Initialize a new Git repository in your current directory
git init

# 2. Add your GitHub repository as the remote origin
# Replace YOUR_USERNAME with your GitHub username
# Replace REPOSITORY_NAME with your repository name
git remote add origin https://github.com/YOUR_USERNAME/REPOSITORY_NAME.git

# 3. Create and switch to main branch (if not already on it)
git branch -M main

# 4. Stage all your existing files
git add .

# 5. Create your first commit
git commit -m "Initial commit"

# 6. Push your code to GitHub
# -u sets up tracking between local and remote branch
git push -u origin main

# Optional: Verify your remote repository is set correctly
git remote -v
```

###############################################################################

## 🌱 Step 1: Starting from scratch – Create a new branch

```bash
# 1. Switch to the main branch (local)
git checkout main

# 2. Fetch the latest branches and tags from GitHub
git fetch origin

# 3. Reset your local main branch to match origin/main
git reset --hard origin/main

# 4. Create a new feature branch from main
git checkout -b feature/your-feature-name

###############################################################################

## 🛠️ Step 2: While working on your feature branch

```bash
# 1. Check your status (what's changed, what's staged, etc.)
git status

# 2. Stage your changes
git add .

# 3. Commit with a clear message
git commit -m "Add feature: explain what you implemented"

# 4. Push your branch (first time use -u to set upstream)
git push -u origin feature/your-feature-name

# ✅ After the first push, you can just use:
git push

# 📌 Optional: Pull latest changes from main (to avoid conflicts early)
git fetch origin
git merge origin/main

# Or if you prefer cleaner history:
git pull origin main --rebase



###############################################################################

## 🔁 Step 3: Finalizing before merging to main (terminal only)

# 1. Make sure you are on your feature branch
git checkout feature/your-feature-name

# 2. Rebase one last time to get the latest changes from main
git pull origin main --rebase

# 3. Run your tests, validations, etc.

# 4. Switch to main branch
git checkout main

# 5. Pull latest main from remote (just in case)
git pull origin main

# 6. Merge your feature branch into main
git merge feature/your-feature-name

# 7. Push updated main to GitHub
git push origin main


###############################################################################

## 🧹 Step 4: After merging – Clean up (optional)

# 1. Delete your local feature branch
git branch -d feature/your-feature-name

# 2. Delete the branch from GitHub
git push origin --delete feature/your-feature-name

# 3. Recreate a branch from a remote branch
git fetch origin
git checkout -b feature/my-branch origin/feature/my-branch


