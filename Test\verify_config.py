#!/usr/bin/env python3
"""
Simple verification script to confirm the configuration is correct.
"""

import json
import os

def main():
    print("🔍 Verifying Mail_Auto Configuration...")
    
    try:
        # Load config
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)
        
        print("✅ Config file is valid JSON")
        
        # Check preferred language in defaults
        defaults = config.get("defaults", {})
        preferred_language = defaults.get("preferred_language", "Not found")
        
        print(f"📋 Preferred language in defaults: {preferred_language}")
        
        # Check if any document types have preferred_language
        document_types = config.get("document_types", {})
        doc_languages = []
        
        for doc_type, doc_config in document_types.items():
            if "preferred_language" in doc_config:
                doc_languages.append(f"{doc_type}: {doc_config['preferred_language']}")
        
        if doc_languages:
            print("⚠️  Found preferred_language in individual document types:")
            for lang in doc_languages:
                print(f"   - {lang}")
            print("💡 These should be removed since language comes from defaults")
        else:
            print("✅ No individual document type language settings found (correct)")
        
        # Summary
        print(f"\n📊 Summary:")
        print(f"   - Default language: {preferred_language}")
        print(f"   - Individual doc languages: {len(doc_languages)} found")
        
        if preferred_language == "Swedish" and len(doc_languages) == 0:
            print("\n🎉 Configuration is perfect!")
            print("✅ Swedish language set in defaults")
            print("✅ No redundant individual language settings")
            print("🚀 Your script is ready to run!")
            return True
        else:
            print("\n⚠️  Configuration needs attention")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        exit(0)
    else:
        exit(1)
