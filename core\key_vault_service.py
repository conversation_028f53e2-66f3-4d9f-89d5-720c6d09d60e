"""
Azure Key Vault service for secure credential and token management.
Handles storing and retrieving credentials, access tokens, and other sensitive data.
"""

import json
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta

from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from azure.core.exceptions import ResourceNotFoundError, AzureError

from .config import config_manager, KeyVaultConfig

logger = logging.getLogger(__name__)


class KeyVaultService:
    """Service for managing secrets in Azure Key Vault."""
    
    def __init__(self, key_vault_config: Optional[KeyVaultConfig] = None):
        """
        Initialize the Key Vault service.
        
        Args:
            key_vault_config: Key Vault configuration. If None, uses current environment config.
        """
        self.config = key_vault_config or config_manager.config.key_vault
        self._client: Optional[SecretClient] = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Azure Key Vault client with appropriate authentication."""
        try:
            # Choose authentication method based on configuration
            if self.config.use_managed_identity:
                # Use managed identity (for Azure-hosted applications)
                credential = DefaultAzureCredential()
                logger.info("Using managed identity for Key Vault authentication")
            else:
                # Use service principal authentication
                if not all([self.config.tenant_id, self.config.client_id, self.config.client_secret]):
                    raise ValueError(
                        "Service principal authentication requires tenant_id, client_id, and client_secret"
                    )
                
                credential = ClientSecretCredential(
                    tenant_id=self.config.tenant_id,
                    client_id=self.config.client_id,
                    client_secret=self.config.client_secret
                )
                logger.info("Using service principal for Key Vault authentication")
            
            self._client = SecretClient(vault_url=self.config.vault_url, credential=credential)
            logger.info(f"Key Vault client initialized for: {self.config.vault_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Key Vault client: {e}")
            raise
    
    @property
    def client(self) -> SecretClient:
        """Get the Key Vault client, initializing if necessary."""
        if self._client is None:
            self._initialize_client()
        return self._client
    
    def store_tenant_credentials(self, tenant_name: str, credentials: Dict[str, Any]) -> bool:
        """
        Store tenant credentials in Key Vault.
        
        Args:
            tenant_name: Name of the tenant
            credentials: Dictionary containing client_id, client_secret, tenant_id, redirect_uri
            
        Returns:
            True if successful, False otherwise
        """
        try:
            secret_name = f"tenant-{tenant_name}-credentials"
            secret_value = json.dumps(credentials)
            
            self.client.set_secret(secret_name, secret_value)
            logger.info(f"Stored credentials for tenant: {tenant_name}")
            return True
            
        except AzureError as e:
            logger.error(f"Failed to store credentials for tenant {tenant_name}: {e}")
            return False
    
    def get_tenant_credentials(self, tenant_name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve tenant credentials from Key Vault.
        
        Args:
            tenant_name: Name of the tenant
            
        Returns:
            Dictionary containing credentials or None if not found
        """
        try:
            secret_name = f"tenant-{tenant_name}-credentials"
            secret = self.client.get_secret(secret_name)
            
            credentials = json.loads(secret.value)
            logger.info(f"Retrieved credentials for tenant: {tenant_name}")
            return credentials
            
        except ResourceNotFoundError:
            logger.warning(f"Credentials not found for tenant: {tenant_name}")
            return None
        except (AzureError, json.JSONDecodeError) as e:
            logger.error(f"Failed to retrieve credentials for tenant {tenant_name}: {e}")
            return None
    
    def store_token_cache(self, tenant_name: str, token_cache: str) -> bool:
        """
        Store MSAL token cache in Key Vault.
        
        Args:
            tenant_name: Name of the tenant
            token_cache: Serialized token cache from MSAL
            
        Returns:
            True if successful, False otherwise
        """
        try:
            secret_name = f"tenant-{tenant_name}-token-cache"
            
            # Add metadata to track when the cache was stored
            cache_data = {
                "cache": token_cache,
                "stored_at": datetime.utcnow().isoformat(),
                "environment": config_manager.current_environment
            }
            
            secret_value = json.dumps(cache_data)
            self.client.set_secret(secret_name, secret_value)
            logger.info(f"Stored token cache for tenant: {tenant_name}")
            return True
            
        except AzureError as e:
            logger.error(f"Failed to store token cache for tenant {tenant_name}: {e}")
            return False
    
    def get_token_cache(self, tenant_name: str) -> Optional[str]:
        """
        Retrieve MSAL token cache from Key Vault.
        
        Args:
            tenant_name: Name of the tenant
            
        Returns:
            Serialized token cache or None if not found
        """
        try:
            secret_name = f"tenant-{tenant_name}-token-cache"
            secret = self.client.get_secret(secret_name)
            
            cache_data = json.loads(secret.value)
            
            # Check if cache is from current environment
            if cache_data.get("environment") != config_manager.current_environment:
                logger.warning(f"Token cache for {tenant_name} is from different environment")
                return None
            
            logger.info(f"Retrieved token cache for tenant: {tenant_name}")
            return cache_data.get("cache", "")
            
        except ResourceNotFoundError:
            logger.info(f"Token cache not found for tenant: {tenant_name}")
            return None
        except (AzureError, json.JSONDecodeError) as e:
            logger.error(f"Failed to retrieve token cache for tenant {tenant_name}: {e}")
            return None
    
    def store_secret(self, name: str, value: Union[str, Dict[str, Any]]) -> bool:
        """
        Store a generic secret in Key Vault.
        
        Args:
            name: Secret name
            value: Secret value (string or dictionary)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if isinstance(value, dict):
                secret_value = json.dumps(value)
            else:
                secret_value = str(value)
            
            self.client.set_secret(name, secret_value)
            logger.info(f"Stored secret: {name}")
            return True
            
        except AzureError as e:
            logger.error(f"Failed to store secret {name}: {e}")
            return False
    
    def get_secret(self, name: str) -> Optional[str]:
        """
        Retrieve a generic secret from Key Vault.
        
        Args:
            name: Secret name
            
        Returns:
            Secret value or None if not found
        """
        try:
            secret = self.client.get_secret(name)
            logger.info(f"Retrieved secret: {name}")
            return secret.value
            
        except ResourceNotFoundError:
            logger.warning(f"Secret not found: {name}")
            return None
        except AzureError as e:
            logger.error(f"Failed to retrieve secret {name}: {e}")
            return None
    
    def delete_tenant_data(self, tenant_name: str) -> bool:
        """
        Delete all Key Vault data for a tenant.
        
        Args:
            tenant_name: Name of the tenant
            
        Returns:
            True if successful, False otherwise
        """
        try:
            secrets_to_delete = [
                f"tenant-{tenant_name}-credentials",
                f"tenant-{tenant_name}-token-cache"
            ]
            
            success = True
            for secret_name in secrets_to_delete:
                try:
                    self.client.begin_delete_secret(secret_name)
                    logger.info(f"Deleted secret: {secret_name}")
                except ResourceNotFoundError:
                    logger.info(f"Secret not found (already deleted?): {secret_name}")
                except AzureError as e:
                    logger.error(f"Failed to delete secret {secret_name}: {e}")
                    success = False
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete tenant data for {tenant_name}: {e}")
            return False
    
    def list_tenant_secrets(self) -> list[str]:
        """
        List all tenant-related secrets in the Key Vault.
        
        Returns:
            List of tenant names that have secrets stored
        """
        try:
            tenant_names = set()
            
            for secret_properties in self.client.list_properties_of_secrets():
                secret_name = secret_properties.name
                if secret_name.startswith("tenant-") and secret_name.endswith("-credentials"):
                    # Extract tenant name from secret name
                    tenant_name = secret_name[7:-12]  # Remove "tenant-" prefix and "-credentials" suffix
                    tenant_names.add(tenant_name)
            
            return list(tenant_names)
            
        except AzureError as e:
            logger.error(f"Failed to list tenant secrets: {e}")
            return []


# Global Key Vault service instance
key_vault_service = KeyVaultService()
