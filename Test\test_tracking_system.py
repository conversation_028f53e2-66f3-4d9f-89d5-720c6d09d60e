"""Comprehensive test suite for the document tracking system."""

import os
import tempfile
import unittest
from datetime import datetime, date, timedelta
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import core modules
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.tracking import (
    ProcessingEvent, ProcessingStatistics, ProcessingStatus, OperationType,
    TrackingDatabase, DocumentTrackingService, DocumentProcessingLogger,
    get_tracking_service, initialize_tracking
)
from core.tracking.analytics import DocumentAnalytics
from core.tracking.config import TrackingConfig, setup_tracking_for_tenant


class TestTrackingDatabase(unittest.TestCase):
    """Test the tracking database functionality."""
    
    def setUp(self):
        """Set up test database."""
        import uuid
        self.temp_db_path = os.path.join(tempfile.gettempdir(), f"test_tracking_{uuid.uuid4().hex}.db")
        self.db = TrackingDatabase(self.temp_db_path)
    
    def tearDown(self):
        """Clean up test database."""
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)
    
    def test_database_initialization(self):
        """Test that database tables are created correctly."""
        # Database should be initialized without errors
        self.assertIsNotNone(self.db)
        
        # Test that we can create a simple event
        event = ProcessingEvent(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="invoice",
            filename="test.pdf",
            file_size=1024,
            processing_date=datetime.now(),
            status=ProcessingStatus.SUCCESS,
            operation_type=OperationType.FULL_PROCESSING
        )
        
        result = self.db.log_processing_event(event)
        self.assertTrue(result)
    
    def test_event_logging(self):
        """Test logging of processing events."""
        event = ProcessingEvent(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="certificate",
            filename="cert.pdf",
            file_size=2048,
            processing_date=datetime.now(),
            status=ProcessingStatus.SUCCESS,
            operation_type=OperationType.FULL_PROCESSING,
            upload_folder="Certificates/2024/TestCompany",
            notification_recipients=["<EMAIL>"],
            processing_time_ms=1500,
            extracted_data={"company": "TestCompany", "year": "2024"}
        )
        
        # Log the event
        result = self.db.log_processing_event(event)
        self.assertTrue(result)
        
        # Retrieve recent events
        recent_events = self.db.get_recent_events("test_tenant", 10)
        self.assertEqual(len(recent_events), 1)
        
        retrieved_event = recent_events[0]
        self.assertEqual(retrieved_event.tenant_name, "test_tenant")
        self.assertEqual(retrieved_event.document_type, "certificate")
        self.assertEqual(retrieved_event.filename, "cert.pdf")
        self.assertEqual(retrieved_event.status, ProcessingStatus.SUCCESS)
    
    def test_daily_statistics(self):
        """Test daily statistics aggregation."""
        today = date.today()
        
        # Log multiple events for today
        for i in range(3):
            event = ProcessingEvent(
                tenant_name="test_tenant",
                mailbox_email="<EMAIL>",
                document_type="invoice" if i < 2 else "certificate",
                filename=f"test_{i}.pdf",
                file_size=1024 * (i + 1),
                processing_date=datetime.now(),
                status=ProcessingStatus.SUCCESS if i < 2 else ProcessingStatus.FAILED,
                operation_type=OperationType.FULL_PROCESSING
            )
            self.db.log_processing_event(event)
        
        # Get daily statistics
        stats = self.db.get_daily_statistics("test_tenant", today, today)
        self.assertEqual(len(stats), 1)
        
        daily_stat = stats[0]
        self.assertEqual(daily_stat.total_documents, 3)
        self.assertEqual(daily_stat.successful_documents, 2)
        self.assertEqual(daily_stat.failed_documents, 1)
        self.assertEqual(daily_stat.documents_by_type["invoice"], 2)
        self.assertEqual(daily_stat.documents_by_type["certificate"], 1)
    
    def test_failed_events_retrieval(self):
        """Test retrieval of failed events."""
        # Log a failed event
        failed_event = ProcessingEvent(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="unknown",
            filename="corrupted.pdf",
            file_size=512,
            processing_date=datetime.now(),
            status=ProcessingStatus.FAILED,
            operation_type=OperationType.FULL_PROCESSING,
            error_message="File could not be processed"
        )
        self.db.log_processing_event(failed_event)
        
        # Retrieve failed events
        failed_events = self.db.get_failed_events("test_tenant", 7)
        self.assertEqual(len(failed_events), 1)
        
        retrieved_event = failed_events[0]
        self.assertEqual(retrieved_event.status, ProcessingStatus.FAILED)
        self.assertEqual(retrieved_event.error_message, "File could not be processed")


class TestDocumentTrackingService(unittest.TestCase):
    """Test the document tracking service."""
    
    def setUp(self):
        """Set up test tracking service."""
        import uuid
        self.temp_db_path = os.path.join(tempfile.gettempdir(), f"test_service_{uuid.uuid4().hex}.db")
        self.service = DocumentTrackingService(self.temp_db_path, enabled=True)
    
    def tearDown(self):
        """Clean up test database."""
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)
    
    def test_context_manager_success(self):
        """Test successful processing with context manager."""
        with self.service.track_processing(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            filename="test.pdf",
            file_size=1024
        ) as tracker:
            tracker.set_document_type("invoice")
            tracker.set_upload_folder("Invoices/2024/Company")
            tracker.set_notification_recipients(["<EMAIL>"])
        
        # Check that event was logged
        recent_events = self.service.get_recent_events("test_tenant", 1)
        self.assertEqual(len(recent_events), 1)
        
        event = recent_events[0]
        self.assertEqual(event.status, ProcessingStatus.SUCCESS)
        self.assertEqual(event.document_type, "invoice")
        self.assertEqual(event.upload_folder, "Invoices/2024/Company")
    
    def test_context_manager_failure(self):
        """Test failed processing with context manager."""
        try:
            with self.service.track_processing(
                tenant_name="test_tenant",
                mailbox_email="<EMAIL>",
                filename="test.pdf",
                file_size=1024
            ) as tracker:
                tracker.set_document_type("invoice")
                raise ValueError("Processing failed")
        except ValueError:
            pass  # Expected
        
        # Check that failure was logged
        failed_events = self.service.get_failed_events("test_tenant", 1)
        self.assertEqual(len(failed_events), 1)
        
        event = failed_events[0]
        self.assertEqual(event.status, ProcessingStatus.FAILED)
        self.assertIn("Processing failed", event.error_message)
    
    def test_simple_event_logging(self):
        """Test simple event logging without context manager."""
        result = self.service.log_simple_event(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="certificate",
            filename="cert.pdf",
            file_size=2048,
            status=ProcessingStatus.SUCCESS,
            operation_type=OperationType.UPLOAD,
            upload_folder="Certificates/2024/Company"
        )
        
        self.assertTrue(result)
        
        # Verify event was logged
        recent_events = self.service.get_recent_events("test_tenant", 1)
        self.assertEqual(len(recent_events), 1)
        
        event = recent_events[0]
        self.assertEqual(event.operation_type, OperationType.UPLOAD)
        self.assertEqual(event.upload_folder, "Certificates/2024/Company")
    
    def test_disabled_service(self):
        """Test that disabled service doesn't log events."""
        disabled_service = DocumentTrackingService(enabled=False)
        
        result = disabled_service.log_simple_event(
            tenant_name="test_tenant",
            mailbox_email="<EMAIL>",
            document_type="invoice",
            filename="test.pdf",
            file_size=1024,
            status=ProcessingStatus.SUCCESS
        )
        
        # Should return True but not actually log anything
        self.assertTrue(result)
        
        # Should return empty lists
        self.assertEqual(len(disabled_service.get_recent_events("test_tenant")), 0)
        self.assertEqual(len(disabled_service.get_failed_events("test_tenant")), 0)


class TestDocumentProcessingLogger(unittest.TestCase):
    """Test the document processing logger."""
    
    def setUp(self):
        """Set up test logger."""
        import uuid
        self.temp_db_path = os.path.join(tempfile.gettempdir(), f"test_logger_{uuid.uuid4().hex}.db")
        initialize_tracking(self.temp_db_path, enabled=True)
        self.logger = DocumentProcessingLogger("test_tenant")
    
    def tearDown(self):
        """Clean up test database."""
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)
    
    @patch('builtins.print')
    def test_log_processing_start(self, mock_print):
        """Test logging of processing start."""
        self.logger.log_processing_start(
            filename="test.pdf",
            mailbox_email="<EMAIL>",
            file_size=1024,
            mime_type="application/pdf"
        )
        
        # Should print a message
        mock_print.assert_called()
        args = mock_print.call_args[0][0]
        self.assertIn("Starting processing", args)
        self.assertIn("test.pdf", args)
    
    @patch('builtins.print')
    def test_log_classification_result(self, mock_print):
        """Test logging of classification results."""
        self.logger.log_classification_result(
            filename="invoice.pdf",
            mailbox_email="<EMAIL>",
            detected_type="invoice",
            confidence=0.95,
            extracted_data={"company": "TestCorp", "amount": "1000"},
            processing_time_ms=500
        )
        
        # Should print success message
        mock_print.assert_called()
        args = mock_print.call_args[0][0]
        self.assertIn("Classification success", args)
        self.assertIn("invoice", args)
        
        # Should log classification event
        tracking_service = get_tracking_service()
        recent_events = tracking_service.get_recent_events("test_tenant", 1)
        self.assertEqual(len(recent_events), 1)
        
        event = recent_events[0]
        self.assertEqual(event.operation_type, OperationType.CLASSIFICATION)
        self.assertEqual(event.status, ProcessingStatus.SUCCESS)


class TestDocumentAnalytics(unittest.TestCase):
    """Test the document analytics functionality."""
    
    def setUp(self):
        """Set up test analytics."""
        import uuid
        self.temp_db_path = os.path.join(tempfile.gettempdir(), f"test_analytics_{uuid.uuid4().hex}.db")
        initialize_tracking(self.temp_db_path, enabled=True)
        self.service = DocumentTrackingService(self.temp_db_path, enabled=True)
        self.analytics = DocumentAnalytics("test_tenant")

        # Create some test data
        self._create_test_data()
    
    def tearDown(self):
        """Clean up test database."""
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)
    
    def _create_test_data(self):
        """Create test data for analytics."""
        today = date.today()
        
        # Create events for the last 7 days
        for days_ago in range(7):
            event_date = today - timedelta(days=days_ago)
            
            for i in range(2):  # 2 events per day
                event = ProcessingEvent(
                    tenant_name="test_tenant",
                    mailbox_email="<EMAIL>",
                    document_type="invoice" if i == 0 else "certificate",
                    filename=f"doc_{days_ago}_{i}.pdf",
                    file_size=1024 * (i + 1),
                    processing_date=datetime.combine(event_date, datetime.min.time()),
                    status=ProcessingStatus.SUCCESS,
                    operation_type=OperationType.FULL_PROCESSING
                )
                self.service.log_event(event)
    
    def test_dashboard_summary(self):
        """Test dashboard summary generation."""
        summary = self.analytics.get_dashboard_summary(days_back=7)
        
        self.assertIn("period", summary)
        self.assertIn("totals", summary)
        self.assertIn("document_types", summary)
        self.assertIn("daily_trend", summary)
        
        # Should have 14 total documents (2 per day for 7 days)
        self.assertEqual(summary["totals"]["documents_processed"], 14)
        self.assertEqual(summary["totals"]["successful_documents"], 14)
        self.assertEqual(summary["totals"]["failed_documents"], 0)
        self.assertEqual(summary["totals"]["success_rate_percent"], 100.0)
        
        # Should have both document types
        self.assertEqual(summary["document_types"]["invoice"], 7)
        self.assertEqual(summary["document_types"]["certificate"], 7)
    
    def test_document_type_analysis(self):
        """Test document type analysis."""
        analysis = self.analytics.get_document_type_analysis(days_back=7)
        
        self.assertIn("document_types", analysis)
        self.assertIn("total_documents", analysis)
        
        self.assertEqual(analysis["total_documents"], 14)
        self.assertEqual(analysis["document_types"]["invoice"]["count"], 7)
        self.assertEqual(analysis["document_types"]["certificate"]["count"], 7)
        self.assertEqual(analysis["document_types"]["invoice"]["percentage"], 50.0)
        self.assertEqual(analysis["document_types"]["certificate"]["percentage"], 50.0)


class TestTrackingConfig(unittest.TestCase):
    """Test tracking configuration management."""
    
    def test_default_config(self):
        """Test default configuration values."""
        tenant_config = {
            "tenant_name": "test_tenant"
        }
        
        config = TrackingConfig(tenant_config)
        
        # Should use defaults when no tracking config is provided
        self.assertTrue(config.enabled)
        self.assertEqual(config.retention_days, 365)
        self.assertTrue(config.analytics_enabled)
        self.assertEqual(config.dashboard_days_back, 30)
        self.assertEqual(config.export_format, "json")
    
    def test_custom_config(self):
        """Test custom configuration values."""
        tenant_config = {
            "tenant_name": "test_tenant",
            "defaults": {
                "tracking": {
                    "enabled": False,
                    "retention_days": 180,
                    "analytics": {
                        "enabled": True,
                        "dashboard_days_back": 14,
                        "export_format": "csv"
                    }
                }
            }
        }
        
        config = TrackingConfig(tenant_config)
        
        self.assertFalse(config.enabled)
        self.assertEqual(config.retention_days, 180)
        self.assertTrue(config.analytics_enabled)
        self.assertEqual(config.dashboard_days_back, 14)
        self.assertEqual(config.export_format, "csv")
    
    def test_database_path_generation(self):
        """Test database path generation."""
        tenant_config = {
            "tenant_name": "test_tenant"
        }
        
        config = TrackingConfig(tenant_config)
        db_path = config.database_path
        
        self.assertIn("test_tenant", db_path)
        self.assertTrue(db_path.endswith(".db"))


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestTrackingDatabase,
        TestDocumentTrackingService,
        TestDocumentProcessingLogger,
        TestDocumentAnalytics,
        TestTrackingConfig
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
