"""Database models for document processing tracking and analytics."""

import sqlite3
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import os


class ProcessingStatus(Enum):
    """Status of document processing operations."""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"  # Some operations succeeded, others failed


class OperationType(Enum):
    """Types of operations that can be tracked."""
    CLASSIFICATION = "classification"
    UPLOAD = "upload"
    NOTIFICATION = "notification"
    FULL_PROCESSING = "full_processing"


@dataclass
class ProcessingEvent:
    """Represents a single document processing event."""
    tenant_name: str
    mailbox_email: str
    document_type: str
    filename: str
    file_size: bytes
    processing_date: datetime
    status: ProcessingStatus
    operation_type: OperationType
    upload_folder: Optional[str] = None
    notification_recipients: Optional[List[str]] = None
    error_message: Optional[str] = None
    processing_time_ms: Optional[int] = None
    extracted_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "tenant_name": self.tenant_name,
            "mailbox_email": self.mailbox_email,
            "document_type": self.document_type,
            "filename": self.filename,
            "file_size": self.file_size,
            "processing_date": self.processing_date.isoformat(),
            "status": self.status.value,
            "operation_type": self.operation_type.value,
            "upload_folder": self.upload_folder,
            "notification_recipients": self.notification_recipients,
            "error_message": self.error_message,
            "processing_time_ms": self.processing_time_ms,
            "extracted_data": self.extracted_data
        }


@dataclass
class ProcessingStatistics:
    """Aggregated processing statistics for a time period."""
    tenant_name: str
    period_start: date
    period_end: date
    total_documents: int
    successful_documents: int
    failed_documents: int
    documents_by_type: Dict[str, int]
    total_file_size: int
    average_processing_time_ms: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_documents == 0:
            return 0.0
        return (self.successful_documents / self.total_documents) * 100


class TrackingDatabase:
    """Database manager for document processing tracking."""
    
    def __init__(self, db_path: str = None):
        """Initialize database connection and create tables if needed."""
        if db_path is None:
            # Default to a tracking directory in the project root
            tracking_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tracking")
            os.makedirs(tracking_dir, exist_ok=True)
            db_path = os.path.join(tracking_dir, "document_tracking.db")
        
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Create database tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS processing_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tenant_name TEXT NOT NULL,
                    mailbox_email TEXT NOT NULL,
                    document_type TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    processing_date TIMESTAMP NOT NULL,
                    status TEXT NOT NULL,
                    operation_type TEXT NOT NULL,
                    upload_folder TEXT,
                    notification_recipients TEXT,  -- JSON array
                    error_message TEXT,
                    processing_time_ms INTEGER,
                    extracted_data TEXT,  -- JSON object
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS daily_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tenant_name TEXT NOT NULL,
                    date DATE NOT NULL,
                    total_documents INTEGER DEFAULT 0,
                    successful_documents INTEGER DEFAULT 0,
                    failed_documents INTEGER DEFAULT 0,
                    documents_by_type TEXT,  -- JSON object
                    total_file_size INTEGER DEFAULT 0,
                    average_processing_time_ms REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(tenant_name, date)
                )
            """)
            
            # Create indexes for better query performance
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_processing_events_tenant_date 
                ON processing_events(tenant_name, processing_date)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_processing_events_status 
                ON processing_events(status)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_daily_statistics_tenant_date 
                ON daily_statistics(tenant_name, date)
            """)
    
    def log_processing_event(self, event: ProcessingEvent) -> bool:
        """Log a processing event to the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO processing_events (
                        tenant_name, mailbox_email, document_type, filename, file_size,
                        processing_date, status, operation_type, upload_folder,
                        notification_recipients, error_message, processing_time_ms, extracted_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    event.tenant_name,
                    event.mailbox_email,
                    event.document_type,
                    event.filename,
                    event.file_size,
                    event.processing_date.isoformat(),
                    event.status.value,
                    event.operation_type.value,
                    event.upload_folder,
                    json.dumps(event.notification_recipients) if event.notification_recipients else None,
                    event.error_message,
                    event.processing_time_ms,
                    json.dumps(event.extracted_data) if event.extracted_data else None
                ))
                
                # Update daily statistics
                self._update_daily_statistics(event, conn)
                return True
                
        except Exception as e:
            print(f"❌ Failed to log processing event: {e}")
            return False
    
    def _update_daily_statistics(self, event: ProcessingEvent, conn=None):
        """Update daily statistics based on a processing event."""
        event_date = event.processing_date.date()

        if conn is None:
            with sqlite3.connect(self.db_path) as conn:
                self._do_update_daily_statistics(event, event_date, conn)
        else:
            self._do_update_daily_statistics(event, event_date, conn)

    def _do_update_daily_statistics(self, event: ProcessingEvent, event_date, conn):
        """Perform the actual daily statistics update."""
        # Get current statistics for the day
        cursor = conn.execute("""
            SELECT total_documents, successful_documents, failed_documents,
                   documents_by_type, total_file_size, average_processing_time_ms
            FROM daily_statistics
            WHERE tenant_name = ? AND date = ?
        """, (event.tenant_name, event_date.isoformat()))

        row = cursor.fetchone()

        if row:
            # Update existing record
            total_docs, success_docs, failed_docs, doc_types_json, total_size, avg_time = row
            doc_types = json.loads(doc_types_json) if doc_types_json else {}

            # Update counters
            total_docs += 1
            if event.status == ProcessingStatus.SUCCESS:
                success_docs += 1
            else:
                failed_docs += 1

            # Update document type counter
            doc_types[event.document_type] = doc_types.get(event.document_type, 0) + 1

            # Update file size
            total_size += event.file_size

            # Update average processing time
            if event.processing_time_ms and avg_time:
                # Weighted average calculation
                avg_time = ((avg_time * (total_docs - 1)) + event.processing_time_ms) / total_docs
            elif event.processing_time_ms:
                avg_time = event.processing_time_ms

            conn.execute("""
                UPDATE daily_statistics
                SET total_documents = ?, successful_documents = ?, failed_documents = ?,
                    documents_by_type = ?, total_file_size = ?, average_processing_time_ms = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE tenant_name = ? AND date = ?
            """, (
                total_docs, success_docs, failed_docs,
                json.dumps(doc_types), total_size, avg_time,
                event.tenant_name, event_date.isoformat()
            ))
        else:
            # Create new record
            doc_types = {event.document_type: 1}
            success_docs = 1 if event.status == ProcessingStatus.SUCCESS else 0
            failed_docs = 1 if event.status != ProcessingStatus.SUCCESS else 0

            conn.execute("""
                INSERT INTO daily_statistics (
                    tenant_name, date, total_documents, successful_documents, failed_documents,
                    documents_by_type, total_file_size, average_processing_time_ms
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event.tenant_name, event_date.isoformat(), 1, success_docs, failed_docs,
                json.dumps(doc_types), event.file_size, event.processing_time_ms
            ))

    def get_daily_statistics(self, tenant_name: str, start_date: date, end_date: date) -> List[ProcessingStatistics]:
        """Get daily statistics for a tenant within a date range."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT tenant_name, date, total_documents, successful_documents, failed_documents,
                       documents_by_type, total_file_size, average_processing_time_ms
                FROM daily_statistics
                WHERE tenant_name = ? AND date BETWEEN ? AND ?
                ORDER BY date
            """, (tenant_name, start_date.isoformat(), end_date.isoformat()))

            statistics = []
            for row in cursor.fetchall():
                tenant, date_str, total, success, failed, doc_types_json, size, avg_time = row
                doc_types = json.loads(doc_types_json) if doc_types_json else {}

                stats = ProcessingStatistics(
                    tenant_name=tenant,
                    period_start=datetime.fromisoformat(date_str).date(),
                    period_end=datetime.fromisoformat(date_str).date(),
                    total_documents=total,
                    successful_documents=success,
                    failed_documents=failed,
                    documents_by_type=doc_types,
                    total_file_size=size,
                    average_processing_time_ms=avg_time
                )
                statistics.append(stats)

            return statistics

    def get_monthly_statistics(self, tenant_name: str, year: int, month: int) -> ProcessingStatistics:
        """Get aggregated monthly statistics for a tenant."""
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1)
        else:
            end_date = date(year, month + 1, 1)

        # Get the last day of the month
        from calendar import monthrange
        _, last_day = monthrange(year, month)
        end_date = date(year, month, last_day)

        daily_stats = self.get_daily_statistics(tenant_name, start_date, end_date)

        if not daily_stats:
            return ProcessingStatistics(
                tenant_name=tenant_name,
                period_start=start_date,
                period_end=end_date,
                total_documents=0,
                successful_documents=0,
                failed_documents=0,
                documents_by_type={},
                total_file_size=0
            )

        # Aggregate daily statistics
        total_docs = sum(stat.total_documents for stat in daily_stats)
        success_docs = sum(stat.successful_documents for stat in daily_stats)
        failed_docs = sum(stat.failed_documents for stat in daily_stats)
        total_size = sum(stat.total_file_size for stat in daily_stats)

        # Merge document types
        merged_doc_types = {}
        for stat in daily_stats:
            for doc_type, count in stat.documents_by_type.items():
                merged_doc_types[doc_type] = merged_doc_types.get(doc_type, 0) + count

        # Calculate weighted average processing time
        total_time = 0
        time_count = 0
        for stat in daily_stats:
            if stat.average_processing_time_ms:
                total_time += stat.average_processing_time_ms * stat.total_documents
                time_count += stat.total_documents

        avg_time = total_time / time_count if time_count > 0 else None

        return ProcessingStatistics(
            tenant_name=tenant_name,
            period_start=start_date,
            period_end=end_date,
            total_documents=total_docs,
            successful_documents=success_docs,
            failed_documents=failed_docs,
            documents_by_type=merged_doc_types,
            total_file_size=total_size,
            average_processing_time_ms=avg_time
        )

    def get_yearly_statistics(self, tenant_name: str, year: int) -> ProcessingStatistics:
        """Get aggregated yearly statistics for a tenant."""
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)

        daily_stats = self.get_daily_statistics(tenant_name, start_date, end_date)

        if not daily_stats:
            return ProcessingStatistics(
                tenant_name=tenant_name,
                period_start=start_date,
                period_end=end_date,
                total_documents=0,
                successful_documents=0,
                failed_documents=0,
                documents_by_type={},
                total_file_size=0
            )

        # Aggregate daily statistics (same logic as monthly)
        total_docs = sum(stat.total_documents for stat in daily_stats)
        success_docs = sum(stat.successful_documents for stat in daily_stats)
        failed_docs = sum(stat.failed_documents for stat in daily_stats)
        total_size = sum(stat.total_file_size for stat in daily_stats)

        merged_doc_types = {}
        for stat in daily_stats:
            for doc_type, count in stat.documents_by_type.items():
                merged_doc_types[doc_type] = merged_doc_types.get(doc_type, 0) + count

        total_time = 0
        time_count = 0
        for stat in daily_stats:
            if stat.average_processing_time_ms:
                total_time += stat.average_processing_time_ms * stat.total_documents
                time_count += stat.total_documents

        avg_time = total_time / time_count if time_count > 0 else None

        return ProcessingStatistics(
            tenant_name=tenant_name,
            period_start=start_date,
            period_end=end_date,
            total_documents=total_docs,
            successful_documents=success_docs,
            failed_documents=failed_docs,
            documents_by_type=merged_doc_types,
            total_file_size=total_size,
            average_processing_time_ms=avg_time
        )

    def get_recent_events(self, tenant_name: str, limit: int = 50) -> List[ProcessingEvent]:
        """Get recent processing events for a tenant."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT tenant_name, mailbox_email, document_type, filename, file_size,
                       processing_date, status, operation_type, upload_folder,
                       notification_recipients, error_message, processing_time_ms, extracted_data
                FROM processing_events
                WHERE tenant_name = ?
                ORDER BY processing_date DESC
                LIMIT ?
            """, (tenant_name, limit))

            events = []
            for row in cursor.fetchall():
                (tenant, mailbox, doc_type, filename, file_size, proc_date, status, op_type,
                 upload_folder, recipients_json, error_msg, proc_time, extracted_json) = row

                recipients = json.loads(recipients_json) if recipients_json else None
                extracted = json.loads(extracted_json) if extracted_json else None

                event = ProcessingEvent(
                    tenant_name=tenant,
                    mailbox_email=mailbox,
                    document_type=doc_type,
                    filename=filename,
                    file_size=file_size,
                    processing_date=datetime.fromisoformat(proc_date),
                    status=ProcessingStatus(status),
                    operation_type=OperationType(op_type),
                    upload_folder=upload_folder,
                    notification_recipients=recipients,
                    error_message=error_msg,
                    processing_time_ms=proc_time,
                    extracted_data=extracted
                )
                events.append(event)

            return events

    def get_failed_events(self, tenant_name: str, days_back: int = 7) -> List[ProcessingEvent]:
        """Get failed processing events for troubleshooting."""
        from datetime import timedelta

        cutoff_date = datetime.now() - timedelta(days=days_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT tenant_name, mailbox_email, document_type, filename, file_size,
                       processing_date, status, operation_type, upload_folder,
                       notification_recipients, error_message, processing_time_ms, extracted_data
                FROM processing_events
                WHERE tenant_name = ? AND status = ? AND processing_date >= ?
                ORDER BY processing_date DESC
            """, (tenant_name, ProcessingStatus.FAILED.value, cutoff_date.isoformat()))

            events = []
            for row in cursor.fetchall():
                (tenant, mailbox, doc_type, filename, file_size, proc_date, status, op_type,
                 upload_folder, recipients_json, error_msg, proc_time, extracted_json) = row

                recipients = json.loads(recipients_json) if recipients_json else None
                extracted = json.loads(extracted_json) if extracted_json else None

                event = ProcessingEvent(
                    tenant_name=tenant,
                    mailbox_email=mailbox,
                    document_type=doc_type,
                    filename=filename,
                    file_size=file_size,
                    processing_date=datetime.fromisoformat(proc_date),
                    status=ProcessingStatus(status),
                    operation_type=OperationType(op_type),
                    upload_folder=upload_folder,
                    notification_recipients=recipients,
                    error_message=error_msg,
                    processing_time_ms=proc_time,
                    extracted_data=extracted
                )
                events.append(event)

            return events
