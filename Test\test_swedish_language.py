#!/usr/bin/env python3
"""
Test script to verify Swedish language functionality.
This test verifies that the preferred_language setting is correctly extracted
and passed to the ChatGPT API.
"""

import sys
import os
import json

# Add parent directory to path so we can import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.interpreter.chatgpt_api import _build_messages
from core.unified_file_analyzer import analyze_file_bytes

def test_swedish_language_extraction():
    """Test that Swedish language is correctly extracted from config."""
    print("🧪 Testing Swedish language extraction from config...")
    
    try:
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)
        
        # Extract preferred language from defaults
        preferred_language = config.get("defaults", {}).get("preferred_language", "English")
        
        if preferred_language == "Swedish":
            print("✅ Swedish language correctly found in config defaults")
        else:
            print(f"❌ Expected 'Swedish', but found '{preferred_language}' in config defaults")
            
        return preferred_language
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return "English"

def test_swedish_prompt_generation():
    """Test that Swedish language instruction is added to ChatGPT prompt."""
    print("\n🧪 Testing Swedish prompt generation...")
    
    try:
        # Test with Swedish language
        messages = _build_messages("Test email body", "Test PDF content", "Swedish")
        
        system_content = messages[0]["content"]
        
        # Check if Swedish language instruction was added
        if "Swedish" in system_content and "Generate the summary in Swedish" in system_content:
            print("✅ Swedish language instruction correctly added to system prompt")
            print(f"📝 Language instruction found: '...Generate the summary in Swedish language...'")
        else:
            print("❌ Swedish language instruction not found in system prompt")
            print(f"📝 System prompt preview: {system_content[-200:]}")
            
        return True
        
    except Exception as e:
        print(f"❌ Swedish prompt test failed: {e}")
        return False

def test_language_flow():
    """Test the complete language flow from config to ChatGPT."""
    print("\n🧪 Testing complete language flow...")
    
    try:
        # Load config
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            tenant_config = json.load(f)
        
        # Extract language (same as unified_file_analyzer.py does)
        preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")
        print(f"📋 Extracted language from config: {preferred_language}")
        
        # Test message building with extracted language
        messages = _build_messages("Test email", "Test PDF", preferred_language)
        system_content = messages[0]["content"]
        
        if preferred_language.lower() != "english" and preferred_language in system_content:
            print(f"✅ Complete flow working: {preferred_language} language instruction added")
        elif preferred_language.lower() == "english":
            print("✅ Complete flow working: English (default) - no special instruction needed")
        else:
            print(f"❌ Flow broken: {preferred_language} not found in system prompt")
            
    except Exception as e:
        print(f"❌ Language flow test failed: {e}")

if __name__ == "__main__":
    print("🇸🇪 Testing Swedish language functionality...\n")
    
    # Test 1: Config extraction
    language = test_swedish_language_extraction()
    
    # Test 2: Prompt generation
    test_swedish_prompt_generation()
    
    # Test 3: Complete flow
    test_language_flow()
    
    print(f"\n✨ Swedish language test completed!")
    print(f"📋 Current language setting: {language}")
    
    if language == "Swedish":
        print("🎉 Your config is correctly set to Swedish!")
        print("💡 Next time you process a document, the summary should be in Swedish.")
    else:
        print("⚠️  Language is not set to Swedish in the defaults section.")
        print("💡 Make sure 'preferred_language': 'Swedish' is in the 'defaults' section of config.json")
