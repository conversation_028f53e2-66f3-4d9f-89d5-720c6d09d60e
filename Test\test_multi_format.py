"""Test script for multi-format file processing."""

from core.file_processors.factory import file_processor_factory

def test_supported_formats():
    """Test what file formats are supported."""
    print("🧪 Testing Multi-Format File Processing Support\n")
    
    # Test various file types
    test_cases = [
        ("application/pdf", "document.pdf"),
        ("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "document.docx"),
        ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "spreadsheet.xlsx"),
        ("text/plain", "readme.txt"),
        ("image/jpeg", "photo.jpg"),
        ("image/png", "screenshot.png"),
        ("application/vnd.openxmlformats-officedocument.presentationml.presentation", "slides.pptx"),
        ("text/csv", "data.csv"),
        ("text/html", "webpage.html"),
        ("application/json", "config.json"),
        ("unknown/type", "mystery.xyz")
    ]
    
    for mime_type, filename in test_cases:
        processor = file_processor_factory.get_processor(mime_type, filename)
        
        if processor:
            processor_name = processor.__class__.__name__
            print(f"✅ {filename:<15} ({mime_type}) → {processor_name}")
        else:
            print(f"❌ {filename:<15} ({mime_type}) → Not supported")
    
    print(f"\n📊 Summary of supported formats:")
    supported_types = file_processor_factory.get_supported_types()
    
    for processor_name, info in supported_types.items():
        print(f"\n{processor_name}:")
        print(f"  📄 Extensions: {', '.join(info['extensions'])}")
        print(f"  🔗 MIME types: {len(info['mime_types'])} types")

if __name__ == "__main__":
    test_supported_formats()
