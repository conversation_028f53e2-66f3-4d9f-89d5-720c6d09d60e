"""PowerPoint file processor using python-pptx."""

from io import Bytes<PERSON>
from typing import Dict, Any

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

from .base import FileProcessor, ProcessingResult


class PptxProcessor(FileProcessor):
    """PowerPoint file processor for PPTX files."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return [
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/vnd.ms-powerpoint"
        ]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".pptx", ".ppt"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is a PowerPoint file."""
        if not PPTX_AVAILABLE:
            return False
            
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) in ["pptx", "ppt"])
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from PowerPoint file."""
        if not PPTX_AVAILABLE:
            error_msg = "python-pptx library not installed"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
        
        self._log_processing_start(filename)
        
        try:
            # Load presentation from bytes
            prs = Presentation(BytesIO(file_bytes))
            
            text_parts = []
            slide_count = len(prs.slides)
            
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_text = []
                
                # Extract text from all shapes in slide
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text)
                
                if slide_text:
                    text_parts.append(f"=== Slide {slide_num} ===")
                    text_parts.extend(slide_text)
                    text_parts.append("")  # Empty line between slides
            
            extracted_text = "\n".join(text_parts)
            
            metadata = {
                "file_type": "pptx",
                "slide_count": slide_count,
                "processing_method": "python-pptx"
            }
            
            if extracted_text.strip():
                self._log_processing_success(filename, len(extracted_text))
                return ProcessingResult.success_result(extracted_text, metadata)
            else:
                error_msg = "No text content found in presentation"
                self._log_processing_error(filename, error_msg)
                return ProcessingResult.error_result(error_msg, metadata)
                
        except Exception as exc:
            error_msg = f"PowerPoint processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
