"""Excel file processor using openpyxl."""

from io import Bytes<PERSON>
from typing import Dict, Any

try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from .base import FileProcessor, ProcessingResult


class XlsxProcessor(FileProcessor):
    """Excel file processor for XLSX files."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "text/csv"
        ]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".xlsx", ".xls", ".csv"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is an Excel file."""
        if not OPENPYXL_AVAILABLE and self._get_file_extension(filename) != "csv":
            return False
            
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) in ["xlsx", "xls", "csv"])
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from Excel file."""
        self._log_processing_start(filename)
        
        file_ext = self._get_file_extension(filename)
        
        # Handle CSV files separately
        if file_ext == "csv":
            return self._process_csv(file_bytes, filename)
        
        if not OPENPYXL_AVAILABLE:
            error_msg = "openpyxl library not installed"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
        
        try:
            # Load workbook from bytes
            workbook = load_workbook(BytesIO(file_bytes), read_only=True, data_only=True)
            
            text_parts = []
            sheet_info = {}
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_text = []
                row_count = 0
                
                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        row_text = [str(cell) if cell is not None else "" for cell in row]
                        sheet_text.append("\t".join(row_text))
                        row_count += 1
                
                if sheet_text:
                    text_parts.append(f"=== Sheet: {sheet_name} ===")
                    text_parts.extend(sheet_text)
                    text_parts.append("")  # Empty line between sheets
                
                sheet_info[sheet_name] = {
                    "rows": row_count,
                    "max_column": sheet.max_column,
                    "max_row": sheet.max_row
                }
            
            workbook.close()
            
            extracted_text = "\n".join(text_parts)
            
            metadata = {
                "file_type": "xlsx",
                "sheet_count": len(workbook.sheetnames),
                "sheets": sheet_info,
                "processing_method": "openpyxl"
            }
            
            if extracted_text.strip():
                self._log_processing_success(filename, len(extracted_text))
                return ProcessingResult.success_result(extracted_text, metadata)
            else:
                error_msg = "No data found in spreadsheet"
                self._log_processing_error(filename, error_msg)
                return ProcessingResult.error_result(error_msg, metadata)
                
        except Exception as exc:
            error_msg = f"Excel processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _process_csv(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Process CSV files using built-in text processing."""
        try:
            # Try different encodings
            for encoding in ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']:
                try:
                    text = file_bytes.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode CSV with any common encoding")
            
            metadata = {
                "file_type": "csv",
                "encoding": encoding,
                "line_count": len(text.splitlines()),
                "processing_method": "text"
            }
            
            self._log_processing_success(filename, len(text))
            return ProcessingResult.success_result(text, metadata)
            
        except Exception as exc:
            error_msg = f"CSV processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
