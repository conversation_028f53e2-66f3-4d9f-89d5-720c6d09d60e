#!/usr/bin/env python3
"""
Test script to verify dynamic recipient name functionality.
This test verifies that the {recipient_name} placeholder works correctly
and extracts first names from recipient configurations.
"""

import sys
import os
import json

# Add parent directory to path so we can import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notification import _extract_first_name, _merge_notification_settings

def test_first_name_extraction():
    """Test the first name extraction function."""
    print("🧪 Testing first name extraction...")
    
    test_cases = [
        ("<PERSON>", "<PERSON>"),
        ("<PERSON>", "<PERSON>"),
        ("Quality Manager", "Quality"),
        ("AP Team", "AP"),
        ("", "there"),  # Empty name fallback
        ("   ", "there"),  # Whitespace only fallback
        ("SingleName", "SingleName"),
        ("Dr. <PERSON>", "Dr."),
        ("<PERSON>", "<PERSON>")
    ]
    
    all_passed = True
    
    for full_name, expected in test_cases:
        result = _extract_first_name(full_name)
        if result == expected:
            print(f"✅ '{full_name}' → '{result}'")
        else:
            print(f"❌ '{full_name}' → '{result}' (expected '{expected}')")
            all_passed = False
    
    return all_passed

def test_recipient_name_in_templates():
    """Test that the system supports {recipient_name} placeholder through localized templates."""
    print("\n🧪 Testing recipient_name placeholder in templates...")

    try:
        from core.notification import _get_localized_template_fallback

        # Test that localized templates contain the placeholder
        languages = ["English", "Swedish", "German", "French"]
        templates_with_placeholder = []

        for language in languages:
            template = _get_localized_template_fallback(language)
            if "{recipient_name}" in template:
                templates_with_placeholder.append(language)

        print(f"✅ Localized templates with {{recipient_name}}: {templates_with_placeholder}")

        # Check config for any custom templates
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)

        custom_templates = []
        document_types = config.get("document_types", {})
        for doc_type, doc_config in document_types.items():
            template = doc_config.get("notification", {}).get("email_template", "")
            if template:
                custom_templates.append(doc_type)

        if custom_templates:
            print(f"ℹ️  Custom templates found: {custom_templates}")
            print("💡 Custom templates should include {{recipient_name}} placeholder")
        else:
            print("✅ No custom templates - using localized fallbacks with {{recipient_name}}")

        # Success if we have localized templates with the placeholder
        return len(templates_with_placeholder) >= 4

    except Exception as e:
        print(f"❌ Template test failed: {e}")
        return False

def test_notification_settings_merge():
    """Test that notification settings merge correctly."""
    print("\n🧪 Testing notification settings merge...")
    
    try:
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)
        
        # Test merging for a document type with recipients
        settings = _merge_notification_settings(config, "order_confirmation")
        
        recipients = settings.get("recipients", [])
        template = settings.get("email_template", "")
        
        print(f"📋 Recipients found: {len(recipients)}")
        for recipient in recipients:
            name = recipient.get("name", "No name")
            email = recipient.get("email", "No email")
            first_name = _extract_first_name(name)
            print(f"   - {name} ({email}) → First name: '{first_name}'")
        
        if template:
            print(f"📧 Custom template: {template[:50]}...")
            has_placeholder = "{recipient_name}" in template
        else:
            print("📧 No custom template - will use localized fallback")
            # Test localized fallback
            from core.notification import _get_localized_template_fallback
            fallback_template = _get_localized_template_fallback("Swedish")
            has_placeholder = "{recipient_name}" in fallback_template
            print(f"📧 Fallback template: {fallback_template[:50]}...")

        if recipients and has_placeholder:
            print("✅ Notification settings merge test passed")
            return True
        else:
            print("❌ Missing recipients or recipient_name placeholder")
            return False
            
    except Exception as e:
        print(f"❌ Settings merge test failed: {e}")
        return False

def test_template_formatting():
    """Test template formatting with recipient name."""
    print("\n🧪 Testing template formatting...")
    
    try:
        # Simulate template formatting
        template = "Hi {recipient_name},\n\nA new {doc_type} has arrived:\n\n{summary}\n\nPlease review."
        
        test_data = {
            "recipient_name": "Ahmed",
            "doc_type": "invoice",
            "summary": "Invoice #12345 for $1,000 from ACME Corp"
        }
        
        formatted = template.format(**test_data)
        
        print("📧 Formatted email:")
        print("-" * 40)
        print(formatted)
        print("-" * 40)
        
        if "Hi Ahmed," in formatted and "invoice" in formatted:
            print("✅ Template formatting test passed")
            return True
        else:
            print("❌ Template formatting failed")
            return False
            
    except Exception as e:
        print(f"❌ Template formatting test failed: {e}")
        return False

if __name__ == "__main__":
    print("👤 Testing Dynamic Recipient Names...\n")
    
    # Run all tests
    test1 = test_first_name_extraction()
    test2 = test_recipient_name_in_templates()
    test3 = test_notification_settings_merge()
    test4 = test_template_formatting()
    
    # Summary
    tests_passed = sum([test1, test2, test3, test4])
    total_tests = 4
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All dynamic recipient name tests passed!")
        print("✅ No more hard-coded names in email templates")
        print("🚀 System is ready for flexible deployment!")
    else:
        print("⚠️  Some tests failed - check configuration")
    
    exit(0 if tests_passed == total_tests else 1)
