"""Text file processor for plain text files."""

from typing import Dict, Any

from .base import FileProcessor, ProcessingResult


class TextProcessor(FileProcessor):
    """Plain text file processor."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return [
            "text/plain",
            "text/html",
            "text/xml",
            "application/xml",
            "application/json",
            "text/markdown",
            "text/rtf"
        ]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".txt", ".html", ".htm", ".xml", ".json", ".md", ".rtf", ".log"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is a text file."""
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) in [ext[1:] for ext in self.supported_extensions])
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from text file."""
        self._log_processing_start(filename)
        
        try:
            # Try different encodings
            for encoding in ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'ascii']:
                try:
                    text = file_bytes.decode(encoding)
                    
                    metadata = {
                        "file_type": "text",
                        "encoding": encoding,
                        "line_count": len(text.splitlines()),
                        "char_count": len(text),
                        "processing_method": "text_decode"
                    }
                    
                    # Handle different text formats
                    file_ext = self._get_file_extension(filename)
                    if file_ext in ["html", "htm"]:
                        text = self._strip_html_tags(text)
                        metadata["file_type"] = "html"
                    elif file_ext == "xml":
                        metadata["file_type"] = "xml"
                    elif file_ext == "json":
                        metadata["file_type"] = "json"
                    elif file_ext == "md":
                        metadata["file_type"] = "markdown"
                    elif file_ext == "rtf":
                        text = self._strip_rtf_formatting(text)
                        metadata["file_type"] = "rtf"
                    
                    if text.strip():
                        self._log_processing_success(filename, len(text))
                        return ProcessingResult.success_result(text, metadata)
                    else:
                        error_msg = "File contains no readable text"
                        self._log_processing_error(filename, error_msg)
                        return ProcessingResult.error_result(error_msg, metadata)
                        
                except UnicodeDecodeError:
                    continue
            
            # If all encodings failed
            error_msg = "Unable to decode text file with any common encoding"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
            
        except Exception as exc:
            error_msg = f"Text processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _strip_html_tags(self, html_text: str) -> str:
        """Simple HTML tag removal."""
        import re
        # Remove HTML tags
        clean = re.compile('<.*?>')
        text = re.sub(clean, '', html_text)
        # Decode HTML entities
        import html
        return html.unescape(text)
    
    def _strip_rtf_formatting(self, rtf_text: str) -> str:
        """Simple RTF formatting removal."""
        import re
        # Remove RTF control words and braces
        text = re.sub(r'\\[a-z]+\d*\s*', ' ', rtf_text)
        text = re.sub(r'[{}]', '', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
