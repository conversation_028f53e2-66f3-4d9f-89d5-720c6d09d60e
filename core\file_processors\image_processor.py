"""Image file processor using OCR."""

from io import <PERSON><PERSON><PERSON>
from typing import Dict, Any

try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

from .base import FileProcessor, ProcessingResult


class ImageProcessor(FileProcessor):
    """Image file processor with OCR text extraction."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return [
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/gif",
            "image/bmp",
            "image/tiff",
            "image/webp"
        ]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif", ".webp"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is an image file."""
        if not OCR_AVAILABLE:
            return False
            
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) in [ext[1:] for ext in self.supported_extensions])
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from image using OCR."""
        if not OCR_AVAILABLE:
            error_msg = "PIL/pytesseract libraries not installed"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
        
        self._log_processing_start(filename)
        
        try:
            # Load image from bytes
            image = Image.open(BytesIO(file_bytes))
            
            # Convert to RGB if needed (for better OCR)
            if image.mode not in ('RGB', 'L'):
                image = image.convert('RGB')
            
            # Extract text using OCR
            extracted_text = pytesseract.image_to_string(image, lang='eng')
            
            metadata = {
                "file_type": "image",
                "image_format": image.format,
                "image_mode": image.mode,
                "image_size": image.size,
                "processing_method": "tesseract_ocr"
            }
            
            if extracted_text.strip():
                self._log_processing_success(filename, len(extracted_text))
                return ProcessingResult.success_result(extracted_text, metadata)
            else:
                error_msg = "No text detected in image"
                self._log_processing_error(filename, error_msg)
                return ProcessingResult.error_result(error_msg, metadata)
                
        except Exception as exc:
            error_msg = f"Image OCR processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
