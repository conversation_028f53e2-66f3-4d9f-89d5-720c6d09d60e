"""Base classes for file processing."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class ProcessingResult:
    """Result of file processing operation."""
    
    success: bool
    text: str
    metadata: Dict[str, Any]
    error_message: Optional[str] = None
    
    @classmethod
    def success_result(cls, text: str, metadata: Optional[Dict[str, Any]] = None) -> "ProcessingResult":
        """Create a successful processing result."""
        return cls(
            success=True,
            text=text,
            metadata=metadata or {},
            error_message=None
        )
    
    @classmethod
    def error_result(cls, error_message: str, metadata: Optional[Dict[str, Any]] = None) -> "ProcessingResult":
        """Create an error processing result."""
        return cls(
            success=False,
            text="",
            metadata=metadata or {},
            error_message=error_message
        )


class FileProcessor(ABC):
    """Abstract base class for file processors."""
    
    @property
    @abstractmethod
    def supported_mime_types(self) -> list[str]:
        """Return list of supported MIME types."""
        pass
    
    @property
    @abstractmethod
    def supported_extensions(self) -> list[str]:
        """Return list of supported file extensions (with dots)."""
        pass
    
    @abstractmethod
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this processor can handle the given file."""
        pass
    
    @abstractmethod
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Process the file and extract text content."""
        pass
    
    def _get_file_extension(self, filename: str) -> str:
        """Extract file extension from filename."""
        return filename.lower().split('.')[-1] if '.' in filename else ""
    
    def _log_processing_start(self, filename: str) -> None:
        """Log processing start."""
        print(f"🔄 Processing {self.__class__.__name__}: {filename}")
    
    def _log_processing_success(self, filename: str, text_length: int) -> None:
        """Log successful processing."""
        print(f"✅ {self.__class__.__name__} success: {filename} ({text_length} chars)")
    
    def _log_processing_error(self, filename: str, error: str) -> None:
        """Log processing error."""
        print(f"❌ {self.__class__.__name__} failed: {filename} - {error}")
