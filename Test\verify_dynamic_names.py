#!/usr/bin/env python3
"""
Quick verification that dynamic recipient names are working correctly.
"""

import sys
import os
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🔍 Verifying Dynamic Recipient Names Configuration...\n")
    
    try:
        # Load config
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)
        
        print("✅ Config file loaded successfully")
        
        # Check for hard-coded names
        hard_coded_names = []
        flexible_templates = []
        
        # Check default template
        default_template = config.get("defaults", {}).get("notification", {}).get("email_template", "")
        if "<PERSON> <PERSON>" in default_template or "Hi AP Team" in default_template:
            hard_coded_names.append("defaults")
        elif "{recipient_name}" in default_template:
            flexible_templates.append("defaults")
        
        # Check document type templates
        document_types = config.get("document_types", {})
        for doc_type, doc_config in document_types.items():
            template = doc_config.get("notification", {}).get("email_template", "")
            if template:
                if "Hi Ahmed" in template or "Hi AP Team" in template or "Hi Quality Team" in template:
                    hard_coded_names.append(doc_type)
                elif "{recipient_name}" in template:
                    flexible_templates.append(doc_type)
        
        # Results
        print(f"📧 Templates using {{recipient_name}}: {flexible_templates}")
        if hard_coded_names:
            print(f"⚠️  Templates with hard-coded names: {hard_coded_names}")
        else:
            print("✅ No hard-coded names found!")
        
        # Check recipients
        print(f"\n👥 Checking recipient configurations...")
        total_recipients = 0
        
        for doc_type, doc_config in document_types.items():
            recipients = doc_config.get("notification", {}).get("recipients", [])
            if recipients:
                print(f"   {doc_type}: {len(recipients)} recipients")
                for recipient in recipients:
                    name = recipient.get("name", "No name")
                    email = recipient.get("email", "No email")
                    print(f"      - {name} ({email})")
                    total_recipients += 1
        
        print(f"\n📊 Summary:")
        print(f"   - Flexible templates: {len(flexible_templates)}")
        print(f"   - Hard-coded templates: {len(hard_coded_names)}")
        print(f"   - Total recipients: {total_recipients}")
        
        if len(hard_coded_names) == 0 and len(flexible_templates) > 0:
            print("\n🎉 Perfect! Dynamic recipient names are working!")
            print("✅ All email templates use {recipient_name} placeholder")
            print("✅ No hard-coded names found")
            print("🚀 System is ready for flexible deployment!")
            return True
        else:
            print("\n⚠️  Configuration needs attention")
            if hard_coded_names:
                print("💡 Replace hard-coded names with {recipient_name} placeholder")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
