"""Factory for creating appropriate file processors."""

from typing import Optional, List
from .base import FileProcessor
from .pdf_processor import PDFProcessor
from .docx_processor import DocxProcessor
from .xlsx_processor import XlsxProcessor
from .text_processor import TextProcessor
from .image_processor import ImageProcessor
from .pptx_processor import PptxProcessor


class FileProcessorFactory:
    """Factory class for creating file processors based on file type."""
    
    def __init__(self):
        """Initialize factory with all available processors."""
        self._processors: List[FileProcessor] = [
            PDFProcessor(),
            DocxProcessor(),
            XlsxProcessor(), 
            TextProcessor(),
            ImageProcessor(),
            PptxProcessor()
        ]
    
    def get_processor(self, mime_type: str, filename: str) -> Optional[FileProcessor]:
        """Get appropriate processor for the given file."""
        for processor in self._processors:
            if processor.can_process(mime_type, filename):
                return processor
        return None
    
    def get_supported_types(self) -> dict[str, List[str]]:
        """Get all supported MIME types and extensions."""
        supported = {}
        for processor in self._processors:
            processor_name = processor.__class__.__name__
            supported[processor_name] = {
                "mime_types": processor.supported_mime_types,
                "extensions": processor.supported_extensions
            }
        return supported
    
    def is_supported(self, mime_type: str, filename: str) -> bool:
        """Check if the file type is supported."""
        return self.get_processor(mime_type, filename) is not None


# Global factory instance
file_processor_factory = FileProcessorFactory()
