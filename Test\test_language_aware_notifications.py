#!/usr/bin/env python3
"""
Test script to verify language-aware email notifications.
This test verifies that email subjects and templates are localized
based on the preferred language setting.
"""

import sys
import os
import json

# Add parent directory to path so we can import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notification import _get_localized_subject, _get_localized_template_fallback, _merge_notification_settings

def test_localized_subjects():
    """Test that email subjects are correctly localized."""
    print("🧪 Testing localized email subjects...")
    
    test_cases = [
        ("English", "invoice", "New invoice received"),
        ("Swedish", "invoice", "Nytt invoice mottaget"),
        ("German", "certificate", "Neues certificate erhalten"),
        ("French", "order", "Nouveau order reçu"),
        ("Spanish", "report", "Nuevo report recibido"),
        ("UnknownLanguage", "test", "New test received")  # Fallback test
    ]
    
    all_passed = True
    
    for language, doc_type, expected in test_cases:
        result = _get_localized_subject(doc_type, language)
        if result == expected:
            print(f"✅ {language}: '{doc_type}' → '{result}'")
        else:
            print(f"❌ {language}: '{doc_type}' → '{result}' (expected '{expected}')")
            all_passed = False
    
    return all_passed

def test_localized_templates():
    """Test that email templates are correctly localized."""
    print("\n🧪 Testing localized email templates...")
    
    languages = ["English", "Swedish", "German", "French", "Spanish", "Italian", "Dutch", "Norwegian", "Danish", "Finnish"]
    
    all_passed = True
    
    for language in languages:
        template = _get_localized_template_fallback(language)
        
        # Check that template contains required placeholders
        required_placeholders = ["{recipient_name}", "{doc_type}", "{summary}"]
        missing_placeholders = [p for p in required_placeholders if p not in template]
        
        if missing_placeholders:
            print(f"❌ {language}: Missing placeholders {missing_placeholders}")
            all_passed = False
        else:
            # Check language-specific greeting
            greetings = {
                "English": "Hi ",
                "Swedish": "Hej ",
                "German": "Hallo ",
                "French": "Bonjour ",
                "Spanish": "Hola ",
                "Italian": "Ciao ",
                "Dutch": "Hallo ",
                "Norwegian": "Hei ",
                "Danish": "Hej ",
                "Finnish": "Hei "
            }
            
            expected_greeting = greetings.get(language, "Hi ")
            if expected_greeting in template:
                print(f"✅ {language}: Template with '{expected_greeting.strip()}' greeting")
            else:
                print(f"❌ {language}: Expected greeting '{expected_greeting.strip()}' not found")
                all_passed = False
    
    return all_passed

def test_swedish_configuration():
    """Test that Swedish language works with current config."""
    print("\n🧪 Testing Swedish configuration...")
    
    try:
        config_path = os.path.join("..", "tenants", "prototype", "config.json")
        with open(config_path, "r") as f:
            config = json.load(f)
        
        # Get preferred language
        preferred_language = config.get("defaults", {}).get("preferred_language", "English")
        print(f"📋 Configured language: {preferred_language}")
        
        # Test subject localization
        subject = _get_localized_subject("certificate_of_analysis", preferred_language)
        print(f"📧 Subject: {subject}")
        
        # Test template localization
        template = _get_localized_template_fallback(preferred_language)
        print(f"📝 Template preview: {template[:50]}...")
        
        # Test notification settings merge
        settings = _merge_notification_settings(config, "certificate_of_analysis")
        template_from_config = settings.get("email_template")
        
        if template_from_config:
            print(f"⚠️  Config has custom template: {template_from_config[:30]}...")
            print("💡 Custom templates override localized fallbacks")
        else:
            print("✅ No custom template - will use localized fallback")
        
        if preferred_language == "Swedish":
            if "Nytt" in subject and "Hej" in template:
                print("✅ Swedish localization working correctly")
                return True
            else:
                print("❌ Swedish localization not working")
                return False
        else:
            print(f"ℹ️  Language is {preferred_language}, not Swedish")
            return True
            
    except Exception as e:
        print(f"❌ Swedish configuration test failed: {e}")
        return False

def test_template_formatting():
    """Test template formatting with Swedish content."""
    print("\n🧪 Testing template formatting with Swedish...")
    
    try:
        # Get Swedish template
        template = _get_localized_template_fallback("Swedish")
        
        # Format with test data
        formatted = template.format(
            recipient_name="Ahmed",
            doc_type="certifikat",
            summary="Detta är en svensk sammanfattning av dokumentet."
        )
        
        print("📧 Formatted Swedish email:")
        print("-" * 50)
        print(formatted)
        print("-" * 50)
        
        if "Hej Ahmed," in formatted and "certifikat" in formatted:
            print("✅ Swedish template formatting works correctly")
            return True
        else:
            print("❌ Swedish template formatting failed")
            return False
            
    except Exception as e:
        print(f"❌ Template formatting test failed: {e}")
        return False

if __name__ == "__main__":
    print("🌍 Testing Language-Aware Email Notifications...\n")
    
    # Run all tests
    test1 = test_localized_subjects()
    test2 = test_localized_templates()
    test3 = test_swedish_configuration()
    test4 = test_template_formatting()
    
    # Summary
    tests_passed = sum([test1, test2, test3, test4])
    total_tests = 4
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All language-aware notification tests passed!")
        print("✅ Email subjects localized correctly")
        print("✅ Email templates localized correctly")
        print("✅ Swedish configuration working")
        print("🌍 System supports multilingual notifications!")
    else:
        print("⚠️  Some tests failed - check language configuration")
    
    exit(0 if tests_passed == total_tests else 1)
