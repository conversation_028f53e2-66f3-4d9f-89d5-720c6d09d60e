"""Multi-format file processing for email automation.

This module provides unified file processing capabilities for various file formats
commonly sent via email attachments.

Supported formats:
- PDF (.pdf)
- Word documents (.docx)
- Excel spreadsheets (.xlsx, .xls)
- Text files (.txt, .csv)
- Images (.png, .jpg, .jpeg, .gif, .bmp, .tiff)
- PowerPoint (.pptx)
"""

from .base import FileProcessor, ProcessingResult
from .pdf_processor import PDFProcessor
from .docx_processor import DocxProcessor
from .xlsx_processor import XlsxProcessor
from .text_processor import TextProcessor
from .image_processor import ImageProcessor
from .pptx_processor import PptxProcessor
from .factory import FileProcessorFactory

__all__ = [
    "FileProcessor",
    "ProcessingResult",
    "PDFProcessor", 
    "DocxProcessor",
    "XlsxProcessor",
    "TextProcessor",
    "ImageProcessor",
    "PptxProcessor",
    "FileProcessorFactory"
]
