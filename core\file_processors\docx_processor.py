"""DOCX file processor using python-docx."""

from io import Bytes<PERSON>
from typing import Dict, Any

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

from .base import FileProcessor, ProcessingResult


class DocxProcessor(FileProcessor):
    """DOCX file processor for Word documents."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword"
        ]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".docx", ".doc"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is a DOCX file."""
        if not DOCX_AVAILABLE:
            return False
            
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) in ["docx", "doc"])
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from DOCX file."""
        if not DOCX_AVAILABLE:
            error_msg = "python-docx library not installed"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
        
        self._log_processing_start(filename)
        
        try:
            # Load document from bytes
            doc = Document(BytesIO(file_bytes))
            
            # Extract text from all paragraphs
            text_parts = []
            
            # Extract from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_parts.append(cell.text)
            
            extracted_text = "\n".join(text_parts)
            
            metadata = {
                "file_type": "docx",
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables),
                "processing_method": "python-docx"
            }
            
            if extracted_text.strip():
                self._log_processing_success(filename, len(extracted_text))
                return ProcessingResult.success_result(extracted_text, metadata)
            else:
                error_msg = "No text content found in document"
                self._log_processing_error(filename, error_msg)
                return ProcessingResult.error_result(error_msg, metadata)
                
        except Exception as exc:
            error_msg = f"DOCX processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
