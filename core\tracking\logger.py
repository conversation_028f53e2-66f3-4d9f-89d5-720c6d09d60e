"""Enhanced logging system for document processing with detailed activity tracking."""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

from .service import get_tracking_service
from .models import ProcessingStatus, OperationType


class LogLevel(Enum):
    """Custom log levels for document processing."""
    PROCESSING_START = "PROCESSING_START"
    PROCESSING_SUCCESS = "PROCESSING_SUCCESS"
    PROCESSING_FAILED = "PROCESSING_FAILED"
    UPLOAD_SUCCESS = "UPLOAD_SUCCESS"
    UPLOAD_FAILED = "UPLOAD_FAILED"
    NOTIFICATION_SUCCESS = "NOTIFICATION_SUCCESS"
    NOTIFICATION_FAILED = "NOTIFICATION_FAILED"
    CLASSIFICATION_SUCCESS = "CLASSIFICATION_SUCCESS"
    CLASSIFICATION_FAILED = "CLASSIFICATION_FAILED"


class DocumentProcessingLogger:
    """Enhanced logger for document processing with structured logging and tracking integration."""
    
    def __init__(self, tenant_name: str, logger_name: str = "document_processing"):
        """
        Initialize the document processing logger.
        
        Args:
            tenant_name: Name of the tenant for tracking purposes
            logger_name: Name of the Python logger to use
        """
        self.tenant_name = tenant_name
        self.logger = logging.getLogger(logger_name)
        self.tracking_service = get_tracking_service()
        
        # Configure logger if not already configured
        if not self.logger.handlers:
            self._configure_logger()
    
    def _configure_logger(self):
        """Configure the logger with appropriate formatting."""
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_processing_start(
        self,
        filename: str,
        mailbox_email: str,
        file_size: int,
        mime_type: str = None
    ):
        """Log the start of document processing."""
        message = f"🔄 Starting processing: {filename}"
        details = {
            "event": LogLevel.PROCESSING_START.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "file_size": file_size,
            "mime_type": mime_type,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"{message} | {json.dumps(details)}")
        print(message)  # Also print for console visibility
    
    def log_classification_result(
        self,
        filename: str,
        mailbox_email: str,
        detected_type: str,
        confidence: float = None,
        extracted_data: Dict[str, Any] = None,
        processing_time_ms: int = None
    ):
        """Log document classification results."""
        if detected_type and detected_type != "Unknown":
            message = f"📄 Classification success: {filename} → {detected_type}"
            level = LogLevel.CLASSIFICATION_SUCCESS
            status = ProcessingStatus.SUCCESS
        else:
            message = f"❓ Classification uncertain: {filename} → {detected_type or 'Unknown'}"
            level = LogLevel.CLASSIFICATION_FAILED
            status = ProcessingStatus.FAILED
        
        details = {
            "event": level.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "detected_type": detected_type,
            "confidence": confidence,
            "extracted_data": extracted_data,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"{message} | {json.dumps(details, default=str)}")
        print(message)
        
        # Track classification event
        if self.tracking_service.is_enabled():
            self.tracking_service.log_simple_event(
                tenant_name=self.tenant_name,
                mailbox_email=mailbox_email,
                document_type=detected_type or "Unknown",
                filename=filename,
                file_size=0,  # Size not available at classification stage
                status=status,
                operation_type=OperationType.CLASSIFICATION,
                extracted_data=extracted_data
            )
    
    def log_upload_result(
        self,
        filename: str,
        mailbox_email: str,
        document_type: str,
        upload_folder: str,
        file_size: int,
        success: bool,
        error_message: str = None,
        processing_time_ms: int = None
    ):
        """Log file upload results."""
        if success:
            message = f"✅ Upload success: {filename} → {upload_folder}"
            level = LogLevel.UPLOAD_SUCCESS
            status = ProcessingStatus.SUCCESS
        else:
            message = f"❌ Upload failed: {filename} → {error_message or 'Unknown error'}"
            level = LogLevel.UPLOAD_FAILED
            status = ProcessingStatus.FAILED
        
        details = {
            "event": level.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "document_type": document_type,
            "upload_folder": upload_folder,
            "file_size": file_size,
            "success": success,
            "error_message": error_message,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"{message} | {json.dumps(details)}")
        print(message)
        
        # Track upload event
        if self.tracking_service.is_enabled():
            self.tracking_service.log_simple_event(
                tenant_name=self.tenant_name,
                mailbox_email=mailbox_email,
                document_type=document_type,
                filename=filename,
                file_size=file_size,
                status=status,
                operation_type=OperationType.UPLOAD,
                upload_folder=upload_folder if success else None,
                error_message=error_message
            )
    
    def log_notification_result(
        self,
        filename: str,
        mailbox_email: str,
        document_type: str,
        recipients: List[str],
        success: bool,
        error_message: str = None,
        processing_time_ms: int = None
    ):
        """Log notification sending results."""
        recipient_list = ", ".join(recipients) if recipients else "No recipients"
        
        if success:
            message = f"📧 Notification success: {filename} → {recipient_list}"
            level = LogLevel.NOTIFICATION_SUCCESS
            status = ProcessingStatus.SUCCESS
        else:
            message = f"📧❌ Notification failed: {filename} → {error_message or 'Unknown error'}"
            level = LogLevel.NOTIFICATION_FAILED
            status = ProcessingStatus.FAILED
        
        details = {
            "event": level.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "document_type": document_type,
            "recipients": recipients,
            "success": success,
            "error_message": error_message,
            "processing_time_ms": processing_time_ms,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"{message} | {json.dumps(details)}")
        print(message)
        
        # Track notification event
        if self.tracking_service.is_enabled():
            self.tracking_service.log_simple_event(
                tenant_name=self.tenant_name,
                mailbox_email=mailbox_email,
                document_type=document_type,
                filename=filename,
                file_size=0,  # Size not relevant for notifications
                status=status,
                operation_type=OperationType.NOTIFICATION,
                notification_recipients=recipients,
                error_message=error_message
            )
    
    def log_processing_complete(
        self,
        filename: str,
        mailbox_email: str,
        document_type: str,
        file_size: int,
        upload_success: bool,
        notification_success: bool,
        upload_folder: str = None,
        recipients: List[str] = None,
        total_processing_time_ms: int = None,
        extracted_data: Dict[str, Any] = None
    ):
        """Log the completion of full document processing."""
        # Determine overall success
        overall_success = upload_success and notification_success
        
        if overall_success:
            message = f"🎉 Processing complete: {filename} (Type: {document_type})"
            level = LogLevel.PROCESSING_SUCCESS
            status = ProcessingStatus.SUCCESS
        elif upload_success or notification_success:
            message = f"⚠️ Processing partial: {filename} (Upload: {upload_success}, Notify: {notification_success})"
            level = LogLevel.PROCESSING_SUCCESS  # Still log as success level
            status = ProcessingStatus.PARTIAL
        else:
            message = f"💥 Processing failed: {filename}"
            level = LogLevel.PROCESSING_FAILED
            status = ProcessingStatus.FAILED
        
        details = {
            "event": level.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "document_type": document_type,
            "file_size": file_size,
            "upload_success": upload_success,
            "notification_success": notification_success,
            "upload_folder": upload_folder,
            "recipients": recipients,
            "overall_success": overall_success,
            "total_processing_time_ms": total_processing_time_ms,
            "extracted_data": extracted_data,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"{message} | {json.dumps(details, default=str)}")
        print(message)
        
        # Track full processing event
        if self.tracking_service.is_enabled():
            self.tracking_service.log_simple_event(
                tenant_name=self.tenant_name,
                mailbox_email=mailbox_email,
                document_type=document_type,
                filename=filename,
                file_size=file_size,
                status=status,
                operation_type=OperationType.FULL_PROCESSING,
                upload_folder=upload_folder,
                notification_recipients=recipients,
                extracted_data=extracted_data
            )
    
    def log_error(
        self,
        filename: str,
        mailbox_email: str,
        error_message: str,
        operation: str = "processing",
        document_type: str = "Unknown"
    ):
        """Log processing errors."""
        message = f"💥 Error during {operation}: {filename} - {error_message}"
        
        details = {
            "event": LogLevel.PROCESSING_FAILED.value,
            "tenant": self.tenant_name,
            "mailbox": mailbox_email,
            "filename": filename,
            "document_type": document_type,
            "operation": operation,
            "error_message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.error(f"{message} | {json.dumps(details)}")
        print(message)
        
        # Track error event
        if self.tracking_service.is_enabled():
            self.tracking_service.log_simple_event(
                tenant_name=self.tenant_name,
                mailbox_email=mailbox_email,
                document_type=document_type,
                filename=filename,
                file_size=0,
                status=ProcessingStatus.FAILED,
                operation_type=OperationType.FULL_PROCESSING,
                error_message=error_message
            )


def get_logger(tenant_name: str) -> DocumentProcessingLogger:
    """Get a document processing logger for a specific tenant."""
    return DocumentProcessingLogger(tenant_name)
