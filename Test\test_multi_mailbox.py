#!/usr/bin/env python3
"""
Test script for multi-mailbox functionality.
This script validates the multi-mailbox configuration and processing logic.
"""

import json
import sys
import os

# Add parent directory to path to import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mailbox_manager import MailboxConfigManager

def test_multi_mailbox_config():
    """Test the multi-mailbox configuration loading and processing."""
    
    # Load the actual tenant configuration
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tenants", "prototype", "config.json")
    try:
        with open(config_path, "r") as f:
            config = json.load(f)
        print("✅ Successfully loaded tenant configuration")
    except FileNotFoundError:
        print(f"❌ {config_path} not found")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in {config_path}: {e}")
        return False
    
    # Test MailboxConfigManager
    manager = MailboxConfigManager(config)
    
    # Test mailbox configuration detection
    has_mailbox_config = manager.has_mailbox_configuration()
    print(f"📬 Has mailbox configuration: {has_mailbox_config}")
    
    if not has_mailbox_config:
        print("❌ Multi-mailbox configuration not detected")
        return False
    
    # Test enabled mailboxes
    enabled_mailboxes = manager.get_enabled_mailboxes()
    print(f"📧 Enabled mailboxes: {len(enabled_mailboxes)}")
    
    for mailbox in enabled_mailboxes:
        print(f"  - {mailbox.display_name} ({mailbox.email})")
    
    # Test document type checking
    test_cases = [
        ("<EMAIL>", "certificate"),
        ("<EMAIL>", "invoice"),
        ("<EMAIL>", "invoice"),  # Should be disabled
    ]
    
    print("\n🔍 Testing document type enablement:")
    for mailbox_email, doc_type in test_cases:
        enabled = manager.is_document_type_enabled_for_mailbox(mailbox_email, doc_type)
        print(f"  - {doc_type} in {mailbox_email}: {'✅ Enabled' if enabled else '❌ Disabled'}")
    
    # Test notification settings
    print("\n📧 Testing notification settings:")
    for mailbox_email, doc_type in test_cases:
        should_notify = manager.should_notify_for_document(mailbox_email, doc_type)
        recipients = manager.get_notification_recipients(mailbox_email, doc_type)
        print(f"  - {doc_type} in {mailbox_email}: {'✅ Notify' if should_notify else '❌ No notify'} ({len(recipients)} recipients)")
    
    # Test configuration merging
    print("\n⚙️ Testing configuration merging:")
    merged_config = manager.get_merged_document_config("<EMAIL>", "certificate")
    print(f"  - Certificate <NAME_EMAIL>:")
    print(f"    - Upload: {merged_config.get('actions', {}).get('upload', 'Not set')}")
    print(f"    - Notify: {merged_config.get('actions', {}).get('notify', 'Not set')}")
    print(f"    - Recipients: {len(merged_config.get('notification', {}).get('recipients', []))}")
    
    return True

def test_legacy_compatibility():
    """Test that legacy single-mailbox configurations still work."""
    
    # Test with a legacy configuration (no mailboxes section)
    legacy_config = {
        "defaults": {
            "preferred_language": "English",
            "actions": {
                "upload": True,
                "notify": True
            }
        },
        "document_types": {
            "certificate": {
                "actions": {
                    "upload": True,
                    "notify": True
                },
                "notification": {
                    "enabled": True,
                    "recipients": [
                        {"name": "Test User", "email": "<EMAIL>"}
                    ]
                }
            }
        }
    }
    
    manager = MailboxConfigManager(legacy_config)
    
    # Should not detect mailbox configuration
    has_mailbox_config = manager.has_mailbox_configuration()
    print(f"📧 Legacy config has mailbox configuration: {has_mailbox_config}")
    
    if has_mailbox_config:
        print("❌ Legacy configuration incorrectly detected as multi-mailbox")
        return False
    
    print("✅ Legacy configuration compatibility verified")
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Multi-Mailbox Functionality")
    print("=" * 50)
    
    # Test multi-mailbox configuration
    print("\n1. Testing Multi-Mailbox Configuration:")
    multi_mailbox_success = test_multi_mailbox_config()
    
    # Test legacy compatibility
    print("\n2. Testing Legacy Compatibility:")
    legacy_success = test_legacy_compatibility()
    
    # Summary
    print("\n" + "=" * 50)
    if multi_mailbox_success and legacy_success:
        print("✅ All tests passed! Multi-mailbox functionality is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
