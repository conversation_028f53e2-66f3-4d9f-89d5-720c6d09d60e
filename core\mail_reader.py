"""
Refactored mail reader module for multi-tenant email automation system.
Handles reading emails for specific tenants with their own credentials.
Supports both Key Vault and legacy JSON file credential storage.
Enhanced with multi-mailbox support for production environments.
"""

import json
import os
import requests
import msal
import base64
import logging
from typing import List, Tuple, Dict, Any, Optional

from .config import config_manager
from .tenant_loader import get_tenant_credentials, get_tenant_token_cache, save_tenant_token_cache
from .mailbox_manager import MailboxConfigManager, create_processing_context

logger = logging.getLogger(__name__)


def _is_external_email(sender_email: str, tenant_config: Dict[Any, Any]) -> bool:
    """
    Check if an email is from an external sender based on tenant configuration.

    Args:
        sender_email: The sender's email address
        tenant_config: Tenant configuration dictionary

    Returns:
        True if email is external, False if internal
    """
    email_filtering = tenant_config.get("defaults", {}).get("email_filtering", {})

    # If external-only filtering is disabled, consider all emails as "external" (process all)
    if not email_filtering.get("process_external_only", False):
        return True

    # Extract domain from sender email
    if "@" not in sender_email:
        return True  # Invalid email format, treat as external

    sender_domain = sender_email.split("@")[1].lower()

    # Check against company domains
    company_domains = email_filtering.get("company_domains", [])
    for domain in company_domains:
        if sender_domain == domain.lower():
            return False  # Internal email

    return True  # External email


def read_mail_multi_mailbox(tenant_data: Tuple[str, str, Dict[Any, Any], str]) -> List[Tuple[bytes, str, str, str, str, str, str]]:
    """
    Read emails from multiple configured mailboxes for a tenant.
    Enhanced version that supports multi-mailbox configurations.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)

    Returns:
        List of tuples: (file_bytes, filename, sender, received_time, mail_body, mime_type, mailbox_email)
    """
    tenant_name, creds_source, config_dict, token_cache_source = tenant_data
    tenant_config = config_dict

    # Initialize mailbox manager
    mailbox_manager = MailboxConfigManager(tenant_config)

    # Check if this tenant has multi-mailbox configuration
    if not mailbox_manager.has_mailbox_configuration():
        # Fall back to legacy single-mailbox behavior
        legacy_attachments = read_mail(tenant_data)
        # Add empty mailbox_email for backward compatibility
        return [(file_bytes, filename, sender, received_time, mail_body, mime_type, "")
                for file_bytes, filename, sender, received_time, mail_body, mime_type in legacy_attachments]

    # Get enabled mailboxes
    enabled_mailboxes = mailbox_manager.get_enabled_mailboxes()
    if not enabled_mailboxes:
        print(f"⚠️ No enabled mailboxes configured for tenant {tenant_name}")
        return []

    # Load tenant credentials
    creds = get_tenant_credentials(tenant_name)
    if not creds:
        logger.error(f"Failed to load credentials for tenant {tenant_name}")
        return []

    CLIENT_ID = creds["client_id"]

    # Use environment-specific configuration
    env_config = config_manager.config
    AUTHORITY = env_config.authority
    SCOPE = env_config.scopes

    # Token cache handling
    cache = msal.SerializableTokenCache()
    token_cache_data = get_tenant_token_cache(tenant_name)
    if token_cache_data:
        cache.deserialize(token_cache_data)

    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
        token_cache=cache
    )

    # Try silent login
    accounts = app.get_accounts()
    result = None
    if accounts:
        result = app.acquire_token_silent(SCOPE, account=accounts[0])

    if not result:
        print(f"❌ No cached token available for tenant {tenant_name}. Please re-authenticate.")
        return []

    # Save token cache
    if result and "access_token" in result:
        save_tenant_token_cache(tenant_name, cache.serialize())
        logger.info(f"Authenticated successfully for tenant {tenant_name}")

        headers = {
            "Authorization": f"Bearer {result['access_token']}",
            "Content-Type": "application/json"
        }

        all_attachments = []

        # Process each enabled mailbox
        for mailbox in enabled_mailboxes:
            print(f"\n📬 Processing mailbox: {mailbox.display_name} ({mailbox.email})")

            # Read emails from this specific mailbox
            mailbox_attachments = _read_mailbox_emails(
                headers, mailbox, tenant_config, mailbox_manager, tenant_name
            )
            all_attachments.extend(mailbox_attachments)

        return all_attachments
    else:
        print(f"❌ Authentication failed for tenant {tenant_name}.")
        if result:
            print(result.get("error_description"))
        return []


def _read_mailbox_emails(
    headers: Dict[str, str],
    mailbox: Any,  # MailboxConfig
    tenant_config: Dict[Any, Any],
    mailbox_manager: Any,  # MailboxConfigManager
    tenant_name: str
) -> List[Tuple[bytes, str, str, str, str, str, str]]:
    """
    Read emails from a specific mailbox.

    Args:
        headers: Authentication headers
        mailbox: MailboxConfig object
        tenant_config: Tenant configuration
        mailbox_manager: MailboxConfigManager instance
        tenant_name: Name of the tenant

    Returns:
        List of attachments with mailbox context
    """
    # Use Microsoft Graph API to access specific user's mailbox
    # Format: /users/{userPrincipalName}/messages
    mailbox_url = f"https://graph.microsoft.com/v1.0/users/{mailbox.email}/messages"
    params = {
        "$filter": "isRead eq false",
        "$top": "10",
        "$select": "subject,from,receivedDateTime,isRead,hasAttachments,id"
    }

    response = requests.get(mailbox_url, headers=headers, params=params)

    file_attachments = []

    if response.status_code == 200:
        messages = response.json().get("value", [])
        if not messages:
            print(f"📭 No unread messages found in {mailbox.email}")
            return []

        print(f"📧 Found {len(messages)} unread messages in {mailbox.email}")

        for msg in messages:
            print(f"\nSubject: {msg['subject']}")
            sender_name = msg['from']['emailAddress']['name']
            sender_email = msg['from']['emailAddress']['address']
            sender = f"{sender_name} <{sender_email}>"
            received_time = msg['receivedDateTime']

            print(f"From: {sender}")
            print(f"Received: {received_time}")

            # Check if this is an external email (if filtering is enabled)
            if not _is_external_email(sender_email, tenant_config):
                print("🏢 Internal email detected - skipping processing")
                print("-" * 60)

                # Still mark as read to avoid reprocessing
                _mark_message_as_read(headers, mailbox.email, msg["id"])
                continue

            # Get email body for processing
            body_preview = _get_message_body(headers, mailbox.email, msg["id"])

            if msg.get("hasAttachments"):
                attachments_url = f"https://graph.microsoft.com/v1.0/users/{mailbox.email}/messages/{msg['id']}/attachments"
                attachments_resp = requests.get(attachments_url, headers=headers)

                if attachments_resp.status_code == 200:
                    attachments = attachments_resp.json().get("value", [])
                    for att in attachments:
                        print(f"📎 Found attachment: {att['name']} ({att['contentType']})")
                        if "contentBytes" in att:
                            try:
                                file_bytes = base64.b64decode(att["contentBytes"])
                                file_attachments.append((
                                    file_bytes,
                                    att["name"],
                                    sender,
                                    received_time,
                                    body_preview,
                                    att["contentType"],
                                    mailbox.email  # Include mailbox context
                                ))
                                print(f"✅ Extracted attachment: {att['name']}")
                            except Exception as e:
                                print(f"❌ Failed to decode attachment {att['name']}: {e}")
            else:
                print("📎 No attachments.")

            # Mark email as read
            _mark_message_as_read(headers, mailbox.email, msg["id"])

    elif response.status_code == 403:
        print(f"❌ Access denied to mailbox {mailbox.email}. Check admin permissions.")
    else:
        print(f"❌ Failed to fetch messages from {mailbox.email}: {response.status_code}")
        print(response.text)

    return file_attachments


def _mark_message_as_read(headers: Dict[str, str], mailbox_email: str, message_id: str) -> None:
    """Mark a message as read in a specific mailbox."""
    patch_url = f"https://graph.microsoft.com/v1.0/users/{mailbox_email}/messages/{message_id}"
    patch_data = json.dumps({"isRead": True})
    patch_response = requests.patch(patch_url, headers=headers, data=patch_data)

    if patch_response.status_code == 200:
        print("✅ Marked as read.")
    else:
        print(f"⚠️ Failed to mark as read: {patch_response.status_code}")


def _get_message_body(headers: Dict[str, str], mailbox_email: str, message_id: str) -> str:
    """Get the body preview of a message from a specific mailbox."""
    body_url = f"https://graph.microsoft.com/v1.0/users/{mailbox_email}/messages/{message_id}"
    params = {"$select": "bodyPreview"}

    body_response = requests.get(body_url, headers=headers, params=params)
    if body_response.status_code == 200:
        return body_response.json().get("bodyPreview", "")
    return ""


def read_mail(tenant_data: Tuple[str, str, Dict[Any, Any], str]) -> List[Tuple[bytes, str, str, str, str, str]]:
    """
    Read emails for a specific tenant and return file attachments.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)
        - creds_source: Either file path or "key_vault" for Key Vault storage
        - token_cache_source: Either file path or "key_vault" for Key Vault storage

    Returns:
        List of tuples: (file_bytes, filename, sender, received_time, mail_body, mime_type)
    """
    tenant_name, creds_source, config_dict, token_cache_source = tenant_data
    tenant_config = config_dict

    # Load tenant credentials (supports both Key Vault and legacy files)
    creds = get_tenant_credentials(tenant_name)
    if not creds:
        logger.error(f"Failed to load credentials for tenant {tenant_name}")
        return []
    
    CLIENT_ID = creds["client_id"]
    REDIRECT_URI = creds["redirect_uri"]

    # Use environment-specific configuration
    env_config = config_manager.config
    AUTHORITY = env_config.authority
    SCOPE = env_config.scopes

    # Token cache handling (supports both Key Vault and legacy files)
    cache = msal.SerializableTokenCache()
    token_cache_data = get_tenant_token_cache(tenant_name)
    if token_cache_data:
        cache.deserialize(token_cache_data)
    
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
        token_cache=cache
    )
    
    # Try silent login
    accounts = app.get_accounts()
    result = None
    if accounts:
        result = app.acquire_token_silent(SCOPE, account=accounts[0])
    
    # Fallback: interactive login
    if not result:
        print(f"🔐 Need interactive login for tenant {tenant_name}. Use the code below:")
        flow = app.initiate_device_flow(scopes=SCOPE)
        if "user_code" not in flow:
            print(f"❌ Failed to create device flow for tenant {tenant_name}. Check app registration.")
            return []
        
        print("Go to:", flow["verification_uri"])
        print("User code:", flow["user_code"])
        result = app.acquire_token_by_device_flow(flow)
    
    # Save token cache (supports both Key Vault and legacy files)
    if result and "access_token" in result:
        save_tenant_token_cache(tenant_name, cache.serialize())
        logger.info(f"Authenticated successfully for tenant {tenant_name}")
        print(f"✅ Authenticated successfully for tenant {tenant_name}!")
        
        headers = {
            "Authorization": f"Bearer {result['access_token']}",
            "Content-Type": "application/json"
        }
        
        # Fetch unread emails with attachments
        response = requests.get(
            "https://graph.microsoft.com/v1.0/me/messages?$filter=isRead eq false&$top=10&$select=subject,from,receivedDateTime,isRead,hasAttachments",
            headers=headers
        )
        
        file_attachments = []
        
        if response.status_code == 200:
            messages = response.json().get("value", [])
            if not messages:
                print(f"📭 No unread messages found for tenant {tenant_name}.")
                return []
            
            print(f"\n📬 Processing unread emails for tenant {tenant_name}:\n")
            
            for msg in messages:
                print(f"Subject: {msg['subject']}")
                sender_name = msg['from']['emailAddress']['name']
                sender_email = msg['from']['emailAddress']['address']
                sender = f"{sender_name} <{sender_email}>"
                received_time = msg['receivedDateTime']

                print(f"From: {sender}")
                print(f"Received: {received_time}")

                # Check if this is an external email (if filtering is enabled)
                if not _is_external_email(sender_email, tenant_config):
                    print("🏢 Internal email detected - skipping processing")
                    print("-" * 60)

                    # Still mark as read to avoid reprocessing
                    message_id = msg["id"]
                    patch_url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}"
                    patch_data = json.dumps({"isRead": True})
                    patch_response = requests.patch(patch_url, headers=headers, data=patch_data)

                    if patch_response.status_code == 200:
                        print("✅ Marked internal email as read.\n")
                    else:
                        print(f"⚠️ Failed to mark internal email as read: {patch_response.status_code}")
                    continue

                print("-" * 60)
                
                # Fetch plain-text body preview once per message
                body_preview = ""
                try:
                    body_resp = requests.get(
                        f"https://graph.microsoft.com/v1.0/me/messages/{msg['id']}?$select=bodyPreview",
                        headers=headers,
                        timeout=10,
                    )
                    if body_resp.status_code == 200:
                        body_preview = body_resp.json().get("bodyPreview", "")
                except requests.RequestException as exc:
                    print(f"⚠️  Failed fetching body preview: {exc}")

                if msg.get("hasAttachments"):
                    attachments_url = f"https://graph.microsoft.com/v1.0/me/messages/{msg['id']}/attachments"
                    attachments_resp = requests.get(attachments_url, headers=headers)
                    
                    if attachments_resp.status_code == 200:
                       attachments = attachments_resp.json().get("value", [])
                       for att in attachments:
                           print(f"📎 Found attachment: {att['name']} ({att['contentType']})")
                           if "contentBytes" in att:
                               try:
                                   file_bytes = base64.b64decode(att["contentBytes"])
                                   file_attachments.append((
                                       file_bytes,
                                       att["name"],
                                       sender,
                                       received_time,
                                       body_preview,
                                       att["contentType"]  # Add MIME type
                                   ))
                                   print(f"✅ Extracted attachment: {att['name']}")
                               except Exception as e:
                                   print(f"❌ Failed to decode attachment {att['name']}: {e}")
                else:
                    print("📎 No attachments.")
                
                # Mark email as read
                message_id = msg["id"]
                patch_url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}"
                patch_data = json.dumps({"isRead": True})
                patch_response = requests.patch(patch_url, headers=headers, data=patch_data)
                
                if patch_response.status_code == 200:
                    print("✅ Marked as read.\n")
                else:
                    print(f"⚠️ Failed to mark as read: {patch_response.status_code}")
                    print(patch_response.text)
        else:
            print(f"❌ Failed to fetch messages for tenant {tenant_name}: {response.status_code}")
            print(response.text)
        
        return file_attachments
    else:
        print(f"❌ Authentication failed for tenant {tenant_name}.")
        if result:
            print(result.get("error_description"))
        return []


def get_tenant_mail_headers(tenant_data: Tuple[str, str, Dict[Any, Any], str]) -> Dict[str, str]:
    """
    Get authenticated headers for a tenant's mail operations.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)
        - creds_source: Either file path or "key_vault" for Key Vault storage
        - token_cache_source: Either file path or "key_vault" for Key Vault storage

    Returns:
        Dictionary containing authorization headers
    """
    tenant_name, creds_source, config_dict, token_cache_source = tenant_data

    # Load tenant credentials (supports both Key Vault and legacy files)
    creds = get_tenant_credentials(tenant_name)
    if not creds:
        logger.error(f"Failed to load credentials for tenant {tenant_name}")
        return {}
    
    CLIENT_ID = creds["client_id"]

    # Use environment-specific configuration
    env_config = config_manager.config
    AUTHORITY = env_config.authority
    SCOPE = env_config.scopes

    # Token cache handling (supports both Key Vault and legacy files)
    cache = msal.SerializableTokenCache()
    token_cache_data = get_tenant_token_cache(tenant_name)
    if token_cache_data:
        cache.deserialize(token_cache_data)
    
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
        token_cache=cache
    )
    
    # Try silent login
    accounts = app.get_accounts()
    result = None
    if accounts:
        result = app.acquire_token_silent(SCOPE, account=accounts[0])
    
    if result and "access_token" in result:
        return {
            "Authorization": f"Bearer {result['access_token']}",
            "Content-Type": "application/json"
        }
    else:
        print(f"❌ Failed to get headers for tenant {tenant_name}")
        return {}


def get_tenant_mail_headers_for_mailbox(tenant_data: Tuple[str, str, Dict[Any, Any], str], mailbox_email: str) -> Dict[str, str]:
    """
    Get authenticated headers for a tenant's mail operations with specific mailbox context.
    This is used for sending emails from a specific mailbox.

    Args:
        tenant_data: Tuple of (tenant_name, creds_source, config_dict, token_cache_source)
        mailbox_email: Email address of the mailbox to send from

    Returns:
        Dictionary containing authorization headers with mailbox context
    """
    # For now, return the same headers as the tenant (admin access allows sending from any mailbox)
    # In the future, this could be enhanced to handle mailbox-specific authentication
    headers = get_tenant_mail_headers(tenant_data)

    # Add mailbox context for logging/debugging
    if headers:
        logger.info(f"Got mail headers for tenant with mailbox context: {mailbox_email}")

    return headers
