"""PDF file processor using existing PDF reader logic."""

from io import BytesIO
from typing import Dict, Any

from pdfminer.high_level import extract_text as pdf_extract_text
from pdf2image import convert_from_bytes
import pytesseract

from .base import FileProcessor, ProcessingResult


class PDFProcessor(FileProcessor):
    """PDF file processor with OCR fallback."""
    
    @property
    def supported_mime_types(self) -> list[str]:
        return ["application/pdf"]
    
    @property 
    def supported_extensions(self) -> list[str]:
        return [".pdf"]
    
    def can_process(self, mime_type: str, filename: str) -> bool:
        """Check if this is a PDF file."""
        return (mime_type in self.supported_mime_types or 
                self._get_file_extension(filename) == "pdf")
    
    def process(self, file_bytes: bytes, filename: str) -> ProcessingResult:
        """Extract text from PDF using pdfminer + OCR fallback."""
        self._log_processing_start(filename)
        
        try:
            text = self._extract_text(file_bytes)
            metadata = {
                "file_type": "pdf",
                "processing_method": "pdfminer" if text else "ocr_fallback"
            }
            
            if text:
                self._log_processing_success(filename, len(text))
                return ProcessingResult.success_result(text, metadata)
            else:
                error_msg = "No text could be extracted from PDF"
                self._log_processing_error(filename, error_msg)
                return ProcessingResult.error_result(error_msg, metadata)
                
        except Exception as exc:
            error_msg = f"PDF processing failed: {exc}"
            self._log_processing_error(filename, error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _extract_text(self, pdf_bytes: bytes) -> str:
        """Extract text from PDF with fallback strategy."""
        # Try pdfminer first
        try:
            miner_text = pdf_extract_text(BytesIO(pdf_bytes)) or ""
            if miner_text.strip():
                return miner_text
        except Exception as exc:
            print(f"⚠️ pdfminer failed: {exc}")
        
        # Fallback to OCR
        try:
            images = convert_from_bytes(pdf_bytes)
            ocr_parts: list[str] = []
            for page_num, img in enumerate(images, start=1):
                page_text = pytesseract.image_to_string(img, lang="eng")
                ocr_parts.append(page_text)
            return "\n".join(ocr_parts)
        except Exception as exc:
            print(f"❌ OCR fallback failed: {exc}")
            return ""
