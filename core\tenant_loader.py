"""
Tenant loader module for multi-tenant email automation system.
Handles loading tenant configurations and credentials from both Key Vault and legacy JSON files.
"""

import os
import json
import logging
from typing import List, Tuple, Dict, Any, Optional

from .config import config_manager
from .key_vault_service import key_vault_service

logger = logging.getLogger(__name__)


def list_tenants(tenants_dir: str = "tenants") -> List[Tuple[str, str, Dict[Any, Any], str]]:
    """
    List all tenants and return their configuration data.
    Supports both Key Vault and legacy JSON file storage.

    Args:
        tenants_dir: Directory containing tenant folders

    Returns:
        List of tuples: (tenant_name, creds_source, config_dict, token_cache_source)
        - creds_source: Either file path or "key_vault" for Key Vault storage
        - token_cache_source: Either file path or "key_vault" for Key Vault storage
    """
    tenants = []

    # If Key Vault is enabled, get tenants from Key Vault
    if config_manager.config.use_key_vault:
        try:
            kv_tenant_names = key_vault_service.list_tenant_secrets()
            logger.info(f"Found {len(kv_tenant_names)} tenants in Key Vault")

            for tenant_name in kv_tenant_names:
                # Load config from local file system (configs are not sensitive)
                config_dict = {}
                config_path = os.path.join(tenants_dir, tenant_name, "config.json")

                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config_dict = json.load(f)
                    except (json.JSONDecodeError, IOError) as e:
                        logger.warning(f"Could not load config for tenant {tenant_name}: {e}")
                        continue
                else:
                    logger.warning(f"No config file found for Key Vault tenant {tenant_name}")
                    continue

                # Use "key_vault" as source indicator
                tenants.append((tenant_name, "key_vault", config_dict, "key_vault"))

        except Exception as e:
            logger.error(f"Failed to load tenants from Key Vault: {e}")
            logger.info("Falling back to local file system")

    # Also check local file system for legacy tenants or if Key Vault is disabled
    if os.path.exists(tenants_dir):
        for tenant_name in os.listdir(tenants_dir):
            tenant_path = os.path.join(tenants_dir, tenant_name)

            if not os.path.isdir(tenant_path):
                continue

            # Paths for tenant files
            creds_path = os.path.join(tenant_path, "credentials.json")
            config_path = os.path.join(tenant_path, "config.json")
            token_cache_path = os.path.join(tenant_path, "token_cache.json")

            # Skip if this tenant is already loaded from Key Vault
            if config_manager.config.use_key_vault and any(t[0] == tenant_name for t in tenants):
                continue

            # Load config if it exists
            config_dict = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_dict = json.load(f)
                except (json.JSONDecodeError, IOError) as e:
                    logger.warning(f"Could not load config for tenant {tenant_name}: {e}")
                    continue

            # Only include tenant if credentials file exists
            if os.path.exists(creds_path):
                tenants.append((tenant_name, creds_path, config_dict, token_cache_path))
            else:
                logger.warning(f"No credentials found for tenant {tenant_name}")

    logger.info(f"Loaded {len(tenants)} tenants total")
    return tenants


def load_tenant_config(tenant_name: str, tenants_dir: str = "tenants") -> Dict[Any, Any]:
    """
    Load configuration for a specific tenant.
    
    Args:
        tenant_name: Name of the tenant
        tenants_dir: Directory containing tenant folders
        
    Returns:
        Configuration dictionary for the tenant
    """
    config_path = os.path.join(tenants_dir, tenant_name, "config.json")
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found for tenant {tenant_name}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        raise RuntimeError(f"Could not load config for tenant {tenant_name}: {e}")


def get_tenant_credentials_path(tenant_name: str, tenants_dir: str = "tenants") -> str:
    """
    Get the credentials file path for a specific tenant.
    DEPRECATED: Use get_tenant_credentials() instead for Key Vault support.

    Args:
        tenant_name: Name of the tenant
        tenants_dir: Directory containing tenant folders

    Returns:
        Path to the tenant's credentials file
    """
    creds_path = os.path.join(tenants_dir, tenant_name, "credentials.json")

    if not os.path.exists(creds_path):
        raise FileNotFoundError(f"Credentials file not found for tenant {tenant_name}")

    return creds_path


def get_tenant_credentials(tenant_name: str, tenants_dir: str = "tenants") -> Optional[Dict[str, Any]]:
    """
    Get credentials for a specific tenant from Key Vault or local file.

    Args:
        tenant_name: Name of the tenant
        tenants_dir: Directory containing tenant folders

    Returns:
        Dictionary containing tenant credentials or None if not found
    """
    # Try Key Vault first if enabled
    if config_manager.config.use_key_vault:
        credentials = key_vault_service.get_tenant_credentials(tenant_name)
        if credentials:
            logger.info(f"Loaded credentials for {tenant_name} from Key Vault")
            return credentials
        else:
            logger.info(f"Credentials for {tenant_name} not found in Key Vault, trying local file")

    # Fall back to local file
    try:
        creds_path = os.path.join(tenants_dir, tenant_name, "credentials.json")
        if os.path.exists(creds_path):
            with open(creds_path, 'r', encoding='utf-8') as f:
                credentials = json.load(f)
            logger.info(f"Loaded credentials for {tenant_name} from local file")
            return credentials
        else:
            logger.error(f"Credentials file not found for tenant {tenant_name}")
            return None
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load credentials for tenant {tenant_name}: {e}")
        return None


def get_tenant_token_cache(tenant_name: str, tenants_dir: str = "tenants") -> Optional[str]:
    """
    Get token cache for a specific tenant from Key Vault or local file.

    Args:
        tenant_name: Name of the tenant
        tenants_dir: Directory containing tenant folders

    Returns:
        Serialized token cache or None if not found
    """
    # Try Key Vault first if enabled
    if config_manager.config.use_key_vault:
        token_cache = key_vault_service.get_token_cache(tenant_name)
        if token_cache:
            logger.info(f"Loaded token cache for {tenant_name} from Key Vault")
            return token_cache
        else:
            logger.info(f"Token cache for {tenant_name} not found in Key Vault, trying local file")

    # Fall back to local file
    try:
        cache_path = os.path.join(tenants_dir, tenant_name, "token_cache.json")
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                token_cache = f.read()
            logger.info(f"Loaded token cache for {tenant_name} from local file")
            return token_cache
        else:
            logger.info(f"Token cache file not found for tenant {tenant_name}")
            return None
    except IOError as e:
        logger.error(f"Failed to load token cache for tenant {tenant_name}: {e}")
        return None


def save_tenant_token_cache(tenant_name: str, token_cache: str, tenants_dir: str = "tenants") -> bool:
    """
    Save token cache for a specific tenant to Key Vault or local file.

    Args:
        tenant_name: Name of the tenant
        token_cache: Serialized token cache
        tenants_dir: Directory containing tenant folders

    Returns:
        True if successful, False otherwise
    """
    # Try Key Vault first if enabled
    if config_manager.config.use_key_vault:
        success = key_vault_service.store_token_cache(tenant_name, token_cache)
        if success:
            logger.info(f"Saved token cache for {tenant_name} to Key Vault")
            return True
        else:
            logger.warning(f"Failed to save token cache for {tenant_name} to Key Vault, trying local file")

    # Fall back to local file
    try:
        tenant_path = os.path.join(tenants_dir, tenant_name)
        os.makedirs(tenant_path, exist_ok=True)

        cache_path = os.path.join(tenant_path, "token_cache.json")
        with open(cache_path, 'w', encoding='utf-8') as f:
            f.write(token_cache)

        logger.info(f"Saved token cache for {tenant_name} to local file")
        return True
    except IOError as e:
        logger.error(f"Failed to save token cache for tenant {tenant_name}: {e}")
        return False
