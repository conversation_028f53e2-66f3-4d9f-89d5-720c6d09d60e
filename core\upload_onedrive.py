"""OneDrive upload helper for multi-tenant email automation system."""

import requests
from typing import Dict


def upload_pdf_to_onedrive(
    pdf_bytes: bytes,
    filename: str,
    headers: Dict[str, str],
    folder: str = "MailUploads",
) -> bool:
    """Upload a PDF file to the specified folder in the user's OneDrive.

    Args:
        pdf_bytes: Binary PDF content.
        filename: Target filename in OneDrive.
        headers: Microsoft Graph authorization headers.
        folder: Folder path in OneDrive (will be created if missing).

    Returns:
        ``True`` on successful upload, otherwise ``False``.
    """
    # 1) Ensure (possibly nested) folder path exists
    def _sanitize(name: str) -> str:
        """Remove characters disallowed by OneDrive ( \", :, *, ?, <, >, | )"""
        ILLEGAL = r'"*:<>?|'
        for c in ILLEGAL:
            name = name.replace(c, "_")
        return name.strip()

    # Walk through each segment and create if missing
    segments = [_sanitize(seg) for seg in folder.strip("/").split("/") if seg]
    parent_path = ""
    for seg in segments:
        parent_path = f"{parent_path}/{seg}" if parent_path else seg
        url = f"https://graph.microsoft.com/v1.0/me/drive/root:/{parent_path}"
        resp = requests.get(url, headers=headers)
        if resp.status_code == 404:
            # Determine correct "children" endpoint depending on whether we are at root level or a nested path
            parent_container = parent_path.rsplit("/", 1)[0] if "/" in parent_path else ""
            if parent_container:
                # Nested path – address it using the driveItem path syntax
                create_url = (
                    f"https://graph.microsoft.com/v1.0/me/drive/root:/{parent_container}:/children"
                )
            else:
                # Top-level (root) folder – use the root/children endpoint
                create_url = "https://graph.microsoft.com/v1.0/me/drive/root/children"
            create_resp = requests.post(
                create_url,
                headers=headers,
                json={"name": seg, "folder": {}, "@microsoft.graph.conflictBehavior": "rename"},
            )
            if create_resp.status_code not in (200, 201):
                print(f"❌ Failed to create folder '{parent_path}': {create_resp.text}")
                return False


    # 2) Upload content
    upload_url = (
        f"https://graph.microsoft.com/v1.0/me/drive/root:/{folder}/{filename}:/content"
    )
    upload_resp = requests.put(upload_url, headers=headers, data=pdf_bytes)
    if upload_resp.status_code in (200, 201):
        print(f"✅ Uploaded PDF to OneDrive: /{folder}/{filename}")
        return True

    print(f"❌ Failed to upload PDF: {upload_resp.status_code}\n{upload_resp.text}")
    return False
